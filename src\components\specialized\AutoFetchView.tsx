import { motion } from "framer-motion";
import React, { memo } from "react";

const AutoFetchView = memo(() => {
  const [viewMode, setViewMode] = React.useState<"summary" | "full">(() => {
    if (typeof window === "undefined") return "summary";
    return (
      (localStorage.getItem("aura-view-mode") as "summary" | "full") ||
      "summary"
    );
  });
  React.useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("aura-view-mode", viewMode);
    }
  }, [viewMode]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.4, ease: [0.22, 1, 0.36, 1] }}
      className="flex h-full flex-col items-center justify-center gap-6 px-4 py-12 md:flex-row"
    >
      <div className="flex w-full max-w-md flex-col items-start text-left md:order-last md:w-1/4">
        <div className="inline-flex items-center gap-2 rounded-[20px] border border-white/70 bg-white/90 px-5 py-2.5 shadow-[0_8px_20px_rgba(0,0,0,0.1)] backdrop-blur-2xl">
          <div
            className="h-1.5 w-1.5 rounded-full"
            style={{ backgroundColor: "#06b6d4" }}
          />
          <div className="flex items-baseline gap-1">
            <span className="text-[10px] font-bold tracking-wider text-slate-800">
              AUTO‑FETCH
            </span>
            <span className="text-[8px] font-medium tracking-wide text-slate-600">
              & CONSENT
            </span>
          </div>
        </div>

        <h2 className="mt-6 text-6xl font-light leading-[1.05] text-white">
          <span className="font-semibold">Link once,</span>
          <br />
          <span className="italic">never chase again</span>
        </h2>
        <div className="mt-6 max-w-md">
          <p className="text-[14px] font-light text-white/90">
            Auto‑fetch records and honor consent with full provenance.
          </p>
          <div
            role="list"
            aria-label="Capabilities"
            className="mt-4 flex flex-wrap gap-2"
          >
            {[
              "ABDM linking",
              "OCR → FHIR",
              "Audit trail",
              "Outside PDFs",
              "Imaging",
              "Labs",
            ].map((cap) => (
              <span
                key={cap}
                role="listitem"
                className="inline-flex items-center gap-1 rounded-full border border-white/60 bg-white/90 px-3 py-1 text-[10px] font-medium text-slate-800 backdrop-blur-md focus:outline-none focus-visible:ring-2 focus-visible:ring-white/80"
                aria-label={cap}
              >
                {cap}
              </span>
            ))}
          </div>
        </div>
      </div>

      <div className="relative w-full flex-1 md:order-first md:w-2/3">
        <div className="relative min-h-0 w-full flex-1 overflow-hidden rounded-xl bg-white/95 shadow-2xl ring-1 ring-slate-200/60 backdrop-blur-sm">
          <div className="border-b border-slate-200 bg-white/80 px-4 py-2 backdrop-blur-sm">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-6">
                <div className="flex items-center gap-2">
                  <img src="/logo.png" alt="Entheory" className="h-6 w-auto" />
                </div>
                <nav className="hidden items-center gap-6 sm:flex">
                  <button className="text-[10px] font-medium text-cyan-600">
                    Records
                  </button>
                  <button className="text-[10px] text-gray-600 hover:text-gray-900">
                    Timeline
                  </button>
                  <button className="text-[10px] text-gray-600 hover:text-gray-900">
                    Summary
                  </button>
                  <button className="text-[10px] text-gray-600 hover:text-gray-900">
                    Actions
                  </button>
                </nav>
              </div>
              <div className="flex items-center gap-3">
                <button className="rounded-lg bg-cyan-500 px-4 py-1.5 text-[10px] font-medium text-white hover:bg-cyan-600 focus:outline-none focus-visible:ring-2 focus-visible:ring-white/70">
                  Sync Now
                </button>
              </div>
            </div>
          </div>

          <div className="border-b border-gray-200 bg-white px-6 py-3">
            <div className="flex flex-wrap items-center justify-between gap-4">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <span className="text-[10px] text-gray-500">Patient:</span>
                  <span className="text-[10px] font-medium text-gray-900">
                    Priya Sharma
                  </span>
                  <span className="rounded bg-green-100 px-2 py-0.5 text-[10px] font-medium text-green-700">
                    ABDM Linked
                  </span>
                  <span className="text-[10px] text-gray-500">17:24 IST</span>
                </div>
                <input
                  type="text"
                  placeholder="Search records..."
                  className="hidden w-48 rounded-lg border border-gray-300 bg-gray-50 px-3 py-1.5 text-[10px] focus:outline-none focus:ring-2 focus:ring-cyan-500 lg:block"
                />
              </div>
              <div className="flex items-center gap-4">
                {/* View mode toggle */}
                <div
                  className="inline-flex overflow-hidden rounded-full border border-gray-300"
                  role="group"
                  aria-label="View mode"
                >
                  <button
                    className={`px-3 py-1 text-[10px] font-medium ${viewMode === "summary" ? "bg-gray-100 text-gray-900" : "bg-white text-gray-600"}`}
                    onClick={() => {
                      setViewMode("summary");
                    }}
                    aria-pressed={viewMode === "summary"}
                  >
                    Summary
                  </button>
                  <button
                    className={`px-3 py-1 text-[10px] font-medium ${viewMode === "full" ? "bg-gray-100 text-gray-900" : "bg-white text-gray-600"}`}
                    onClick={() => {
                      setViewMode("full");
                    }}
                    aria-pressed={viewMode === "full"}
                  >
                    Full
                  </button>
                </div>
                <div className="hidden items-center gap-2 text-[10px] text-gray-600 md:flex">
                  <span className="rounded bg-gray-100 px-2 py-0.5">
                    Consent v1.2
                  </span>
                  <button
                    className="underline hover:text-gray-900"
                    onClick={() => {}}
                  >
                    View audit
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content Area */}
          <div className="flex-1 bg-gray-50 p-2">
            {/* Stage Headers with counts and Conflicts lane */}
            <div className="mb-2 grid grid-cols-5 gap-4">
              <div className="text-center">
                <p className="text-[10px] font-bold uppercase tracking-wider text-gray-600">
                  Fetching (1)
                </p>
              </div>
              <div className="text-center">
                <p className="text-[10px] font-bold uppercase tracking-wider text-gray-600">
                  Processing (1)
                </p>
              </div>
              <div className="text-center">
                <p className="text-[10px] font-bold uppercase tracking-wider text-amber-600">
                  Conflicts (0)
                </p>
              </div>
              <div className="text-center">
                <p className="text-[10px] font-bold uppercase tracking-wider text-gray-600">
                  Linked (1)
                </p>
              </div>
              <div className="text-center">
                <p className="text-[10px] font-bold uppercase tracking-wider text-gray-600">
                  Complete (1)
                </p>
              </div>
            </div>

            {/* Record Cards with Conflicts column */}
            <div className="grid grid-cols-5 gap-4">
              {/* Card 1 - Fetching */}
              <motion.div
                className="flex h-[220px] flex-col rounded-xl border border-gray-200 bg-white p-4 transition-all hover:shadow-xl focus:outline-none focus-visible:ring-2 focus-visible:ring-cyan-500"
                tabIndex={0}
                role="group"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <div className="mb-4 flex items-start justify-between">
                  <h3 className="text-[12px] font-semibold text-gray-900">
                    Apollo Hospital
                  </h3>
                  <span className="rounded-full bg-yellow-100 px-2.5 py-0.5 text-[8px] font-medium text-yellow-700">
                    Fetching
                  </span>
                </div>
                <div className="flex-1 space-y-3">
                  <div className="flex items-center gap-2">
                    <span className="text-[10px] text-gray-500">Type:</span>
                    <span className="text-[10px] font-medium text-gray-700">
                      Discharge Summary
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-[10px] text-gray-500">Date:</span>
                    <span className="text-[10px] font-medium text-gray-700">
                      Aug 15, 2025
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-[10px] text-gray-500">Source:</span>
                    <div className="flex items-center gap-1">
                      <div className="flex h-5 w-5 items-center justify-center rounded bg-cyan-100">
                        <span className="text-[10px] font-bold text-cyan-600">
                          A
                        </span>
                      </div>
                      <span className="text-[10px] font-medium text-gray-700">
                        Apollo EMR
                      </span>
                    </div>
                  </div>
                </div>
                <div className="mt-auto flex items-center justify-between border-t border-gray-100 pt-4">
                  <span className="text-[10px] text-gray-500">
                    MRN: AP-2025-847
                  </span>
                  <div className="flex items-center gap-2">
                    <div className="h-5 w-5 animate-spin rounded-full border-2 border-yellow-400 border-t-transparent"></div>
                  </div>
                </div>
              </motion.div>

              {/* Card 2 - Processing */}
              <motion.div
                className="flex h-[220px] flex-col rounded-xl border border-gray-200 bg-white p-4 transition-all hover:shadow-xl focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500"
                tabIndex={0}
                role="group"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
              >
                <div className="mb-4 flex items-start justify-between">
                  <h3 className="text-[12px] font-semibold text-gray-900">
                    SRL Diagnostics
                  </h3>
                  <span className="rounded-full bg-blue-100 px-2.5 py-0.5 text-[8px] font-medium text-blue-700">
                    Processing
                  </span>
                </div>

                <div className="flex-1 space-y-3">
                  <div className="flex items-center gap-2">
                    <span className="text-[10px] text-gray-500">Type:</span>
                    <span className="text-[10px] font-medium text-gray-700">
                      Pathology Report
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-[10px] text-gray-500">Date:</span>
                    <span className="text-[10px] font-medium text-gray-700">
                      Aug 20, 2025
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-[10px] text-gray-500">Source:</span>
                    <div className="flex items-center gap-1">
                      <div className="flex h-5 w-5 items-center justify-center rounded bg-purple-100">
                        <span className="text-[10px] font-bold text-purple-600">
                          S
                        </span>
                      </div>
                      <span className="text-[10px] font-medium text-gray-700">
                        SRL LIS
                      </span>
                    </div>
                  </div>
                </div>

                <div className="mt-auto flex items-center justify-between border-t border-gray-100 pt-4">
                  <span className="text-[10px] text-gray-500">
                    <span className="rounded bg-cyan-100 px-2 py-1 text-[10px] font-medium text-cyan-700">
                      OCR
                    </span>
                  </span>
                  <div className="flex items-center gap-2">
                    <span className="text-[10px] font-medium text-blue-600">
                      75%
                    </span>
                  </div>
                </div>
              </motion.div>

              {/* Card 3 - Conflicts */}
              <motion.div
                className="flex h-[220px] flex-col items-center justify-center rounded-xl border border-amber-200 bg-white p-4 text-amber-700/80 transition-all hover:shadow-xl focus:outline-none focus-visible:ring-2 focus-visible:ring-amber-400"
                tabIndex={0}
                role="group"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.55 }}
              >
                <div className="text-[10px]">No conflicts</div>
              </motion.div>

              {/* Card 3 - Linked */}
              <motion.div
                className="flex h-[220px] flex-col rounded-xl border border-green-200 bg-white p-4 transition-all hover:shadow-xl focus:outline-none focus-visible:ring-2 focus-visible:ring-green-500"
                tabIndex={0}
                role="group"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
              >
                <div className="mb-4 flex items-start justify-between">
                  <h3 className="text-[12px] font-semibold text-gray-900">
                    Max Healthcare
                  </h3>
                  <span className="rounded-full bg-green-100 px-2.5 py-0.5 text-[8px] font-medium text-green-700">
                    Linked
                  </span>
                </div>

                <div className="flex-1 space-y-3">
                  <div className="flex items-center gap-2">
                    <span className="text-[10px] text-gray-500">Type:</span>
                    <span className="text-[10px] font-medium text-gray-700">
                      CT Scan Report
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-[10px] text-gray-500">Date:</span>
                    <span className="text-[10px] font-medium text-gray-700">
                      Aug 22, 2025
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-[10px] text-gray-500">Source:</span>
                    <div className="flex items-center gap-1">
                      <div className="flex h-5 w-5 items-center justify-center rounded bg-orange-100">
                        <span className="text-[10px] font-bold text-orange-600">
                          M
                        </span>
                      </div>
                      <span className="text-[10px] font-medium text-gray-700">
                        Max PACS
                      </span>
                    </div>
                  </div>
                </div>

                <div className="mt-auto flex items-center justify-between border-t border-gray-100 pt-4">
                  <span className="text-[10px] text-gray-500">
                    ABDM ID: PS@abdm
                  </span>
                  <div className="flex items-center gap-2">
                    <svg
                      className="h-5 w-5 text-green-500"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </div>
                </div>
              </motion.div>

              {/* Card 4 - Complete */}
              <motion.div
                className="flex h-[220px] flex-col rounded-xl border border-green-200 bg-white p-4 transition-all hover:shadow-xl focus:outline-none focus-visible:ring-2 focus-visible:ring-emerald-500"
                tabIndex={0}
                role="group"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7 }}
              >
                <div className="mb-4 flex items-start justify-between">
                  <h3 className="text-[12px] font-semibold text-gray-900">
                    Fortis Hospital
                  </h3>
                  <span className="rounded-full bg-green-100 px-2.5 py-0.5 text-[8px] font-medium text-green-700">
                    Complete
                  </span>
                </div>

                <div className="flex-1 space-y-3">
                  <div className="flex items-center gap-2">
                    <span className="text-[10px] text-gray-500">Type:</span>
                    <span className="text-[10px] font-medium text-gray-700">
                      Oncology Consult
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-[10px] text-gray-500">Date:</span>
                    <span className="text-[10px] font-medium text-gray-700">
                      Aug 24, 2025
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-[10px] text-gray-500">Source:</span>
                    <div className="flex items-center gap-1">
                      <div className="flex h-5 w-5 items-center justify-center rounded bg-green-100">
                        <span className="text-[10px] font-bold text-green-600">
                          F
                        </span>
                      </div>
                      <span className="text-[10px] font-medium text-gray-700">
                        Fortis EMR
                      </span>
                    </div>
                  </div>
                </div>

                <div className="mt-auto flex items-center justify-between border-t border-gray-100 pt-4">
                  <span className="text-[10px] text-gray-500">
                    Dr. R. Mehta
                  </span>
                  <div className="flex items-center gap-2">
                    <button className="text-[10px] font-medium text-cyan-600 hover:text-cyan-700">
                      View →
                    </button>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Pre-visit Readiness Mini-panel */}
            <div className="mt-2 rounded-xl border border-emerald-200 bg-emerald-50 p-2">
              <div className="flex flex-wrap items-center justify-between gap-4">
                <div className="flex items-center gap-3">
                  <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-emerald-100 font-bold text-emerald-700">
                    ✓
                  </div>
                  <div>
                    <p className="text-[10px] font-semibold text-emerald-900">
                      Pre-visit readiness
                    </p>
                    <p className="text-[10px] text-emerald-800/80">
                      2 gaps: recent imaging report, updated medication list
                    </p>
                  </div>
                </div>
                <div className="flex flex-shrink-0 items-center gap-2">
                  <button className="rounded-full border border-emerald-300 bg-white px-2.5 py-0.5 text-[8px] font-medium text-emerald-700 hover:bg-emerald-50 focus:outline-none focus:ring-2 focus:ring-emerald-500">
                    Send reminder
                  </button>
                  <button className="rounded-full border border-emerald-300 bg-white px-2.5 py-0.5 text-[8px] font-medium text-emerald-700 hover:bg-emerald-50 focus:outline-none focus:ring-2 focus:ring-emerald-500">
                    View details
                  </button>
                </div>
              </div>
            </div>

            {/* Bottom Metrics */}
            <div className="mt-2 rounded-xl border border-cyan-200 bg-gradient-to-r from-cyan-50 to-cyan-100 p-2">
              <div className="flex flex-wrap items-center justify-between gap-4">
                <div className="flex items-center gap-4">
                  <div className="hidden h-8 w-8 flex-shrink-0 items-center justify-center rounded-lg bg-white shadow-sm sm:flex">
                    <svg
                      className="h-6 w-6 text-cyan-600"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                      />
                    </svg>
                  </div>
                  <div>
                    <p className="text-[10px] font-semibold text-gray-900">
                      Record Gathering Time
                    </p>
                    <p className="text-[10px] text-gray-600">
                      Target: &lt;2 min per case (from 6-20 min baseline)
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="text-right">
                    <p className="text-[12px] font-light text-cyan-600">1.8</p>
                    <p className="text-[8px] text-gray-600">minutes average</p>
                  </div>
                  <button
                    className="group relative hidden items-center gap-1 rounded-full border border-cyan-200 bg-white px-2.5 py-0.5 text-[8px] font-medium text-cyan-700 shadow-sm hover:bg-cyan-50 focus:outline-none focus:ring-2 focus:ring-cyan-500 lg:inline-flex"
                    aria-describedby="benchmark-tip"
                  >
                    <span className="font-semibold">Benchmark</span>
                    <span className="text-[10px] text-cyan-600">2.8m</span>
                    <div
                      role="tooltip"
                      id="benchmark-tip"
                      className="pointer-events-none absolute bottom-full left-1/2 z-10 mb-2 w-64 -translate-x-1/2 rounded-md bg-slate-900 p-2 text-[10px] text-white opacity-0 transition-opacity duration-150 group-hover:opacity-100 group-focus:opacity-100"
                    >
                      <p className="leading-relaxed">
                        Typical clinics spend 6–20 min collecting records. Top
                        quartile achieves ~2.8 min. Entheory target:{" "}
                        <span className="font-semibold">&lt;2.0 min</span>.
                      </p>
                    </div>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
});

AutoFetchView.displayName = "AutoFetchView";

export default AutoFetchView;
