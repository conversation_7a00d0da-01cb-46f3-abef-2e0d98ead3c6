import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, SheetTrigger } from "@/components/ui/sheet";
import { cn } from "@/lib/utils";
import { Menu, X } from "lucide-react";
import React, { useCallback, useEffect, useState } from "react";

interface NavLink {
  href: string;
  label: string;
  external?: boolean;
}

const Header: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [hasScrolled, setHasScrolled] = useState(false);
  const [scrollDir, setScrollDir] = useState<"up" | "down">("up");

  useEffect(() => {
    let lastScrollY = window.scrollY;
    const handleScroll = () => {
      const currentY = window.scrollY;
      setHasScrolled(currentY > 20);
      if (currentY > lastScrollY) {
        setScrollDir("down");
      } else {
        setScrollDir("up");
      }
      lastScrollY = currentY;
    };
    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const navLinks: NavLink[] = [
    { href: "#solution", label: "Approach" },
    { href: "#features", label: "Product" },
    { href: "#benefits", label: "Advantage" },
    {
      href: "https://forms.office.com/r/rv2nCDByTQ",
      label: "Contribute",
      external: true,
    },
  ];

  const handleNavClick = useCallback((href: string): void => {
    setIsOpen(false);
    if (!href.startsWith("http")) {
      const element = document.querySelector(href);
      if (element) {
        const offset = 80;
        const top =
          element.getBoundingClientRect().top + window.scrollY - offset;
        window.scrollTo({ top, behavior: "smooth" });
      }
    }
  }, []);

  const handlePilotOpen = useCallback((): void => {
    window.dispatchEvent(new Event("early-pilot-open"));
  }, []);

  return (
    <header
      className={cn(
        "fixed left-0 right-0 z-50 transition-all duration-500 ease-in-out",
        scrollDir === "down" ? "-top-24" : hasScrolled ? "top-4" : "top-0"
      )}
      data-animate-header
    >
      <div className="container-optimized mx-auto">
        <nav
          className={cn(
            "flex items-center justify-between p-3 transition-all duration-500 ease-in-out",
            hasScrolled
              ? "rounded-2xl border border-white/30 bg-white/60 shadow-2xl backdrop-blur-2xl backdrop-saturate-150"
              : "border-b border-transparent bg-transparent"
          )}
        >
          <a
            href="#hero"
            aria-label="Go to hero section"
            onClick={(e) => {
              e.preventDefault();
              handleNavClick("#hero");
            }}
            className="flex items-center gap-2 transition-opacity hover:opacity-80"
            data-cursor="pointer"
          >
            <img src="/logo.png" alt="Entheory Logo" className="h-7 w-auto" />
          </a>

          <div className="hidden items-center space-x-6 md:flex">
            {navLinks.map((link) => (
              <a
                key={link.href}
                href={link.href}
                className="text-sm font-medium text-black transition-colors hover:text-foreground"
                onClick={(e) => {
                  if (link.external) return;
                  e.preventDefault();
                  handleNavClick(link.href);
                }}
                data-cursor="pointer"
                {...(link.external && {
                  target: "_blank",
                  rel: "noopener noreferrer",
                })}
              >
                {link.label}
              </a>
            ))}
          </div>

          <div className="flex items-center gap-2">
            <Button
              size="sm"
              className="hidden rounded-full font-semibold md:inline-flex"
              onClick={handlePilotOpen}
              data-cursor="pointer"
            >
              Early Pilot
            </Button>

            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon-lg"
                  className="h-9 w-9 md:hidden"
                  aria-label="Open menu"
                  data-cursor="pointer"
                >
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-[280px] p-0">
                <div className="flex h-full flex-col">
                  <div className="flex items-center justify-between border-b p-4">
                    <span className="font-semibold">Entheory</span>
                    <Button
                      variant="ghost"
                      size="icon-lg"
                      onClick={() => setIsOpen(false)}
                      className="h-8 w-8"
                      aria-label="Close menu"
                      data-cursor="pointer"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex flex-col gap-1 p-4">
                    {navLinks.map((link) => (
                      <a
                        key={link.href}
                        href={link.href}
                        className="rounded-md px-3 py-2 text-base font-medium text-foreground transition-colors hover:bg-muted"
                        onClick={(e) => {
                          if (link.external) return;
                          e.preventDefault();
                          handleNavClick(link.href);
                        }}
                        data-cursor="pointer"
                        {...(link.external && {
                          target: "_blank",
                          rel: "noopener noreferrer",
                        })}
                      >
                        {link.label}
                      </a>
                    ))}
                    <Button
                      className="mt-4"
                      size="lg"
                      onClick={handlePilotOpen}
                      data-cursor="pointer"
                    >
                      Early Pilot Access
                    </Button>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </nav>
      </div>
    </header>
  );
};

export default Header;
