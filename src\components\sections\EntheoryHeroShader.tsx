"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import {
  ArrowRight,
  BotMessageSquare,
  CheckCircle,
  Cpu,
  FileText,
  Lock,
  Play,
  ShieldCheck,
} from "lucide-react";
import React, { memo, useMemo } from "react";

const AnimatedCard: React.FC<{
  icon: React.ReactNode;
  title: string;
  subtitle: string;
  delay: number;
}> = memo(({ icon, title, subtitle, delay }) => (
  <motion.div
    initial={{ opacity: 0, y: 20, scale: 0.95 }}
    animate={{ opacity: 1, y: 0, scale: 1 }}
    transition={{ duration: 0.5, ease: "easeOut", delay }}
    whileHover={{ scale: 1.05 }}
    className="flex items-center gap-4 rounded-xl border border-white/10 bg-white/5 p-3 backdrop-blur-md transition-shadow hover:shadow-lg"
  >
    <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-white/10">
      {icon}
    </div>
    <div>
      <h4 className="font-semibold text-white">{title}</h4>
      <p className="text-sm text-slate-300">{subtitle}</p>
    </div>
  </motion.div>
));

AnimatedCard.displayName = "AnimatedCard";

const EntheoryHeroShader: React.FC = memo(() => {
  const badges = useMemo(
    () => [
      { icon: Cpu, text: "FHIR-native" },
      { icon: ShieldCheck, text: "ABDM-ready" },
      { icon: Lock, text: "PHI-first security" },
    ],
    []
  );

  const workflowSteps = useMemo(
    () => [
      {
        icon: <FileText className="h-5 w-5 text-cyan-300" />,
        title: "Data Ingestion",
        subtitle: "EMR, LIS, PACS & PDFs",
        delay: 0.2,
      },
      {
        icon: <CheckCircle className="h-5 w-5 text-green-300" />,
        title: "Patient Unification",
        subtitle: "One timeline, full context",
        delay: 0.4,
      },
      {
        icon: <BotMessageSquare className="h-5 w-5 text-violet-300" />,
        title: "AI-Powered Summary",
        subtitle: "Ready for tumor board",
        delay: 0.6,
      },
    ],
    []
  );

  return (
    <section className="relative overflow-hidden bg-gradient-to-b from-slate-900 via-cyan-950 to-violet-950 py-20">
      {/* Subtle Animated Overlay */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-tr from-cyan-700/20 via-transparent to-violet-700/20"
        animate={{ opacity: [0.6, 0.3, 0.6] }}
        transition={{ duration: 10, repeat: Infinity, repeatType: "mirror" }}
      />

      {/* Content */}
      <div className="relative z-10 flex items-center">
        <div className="container-optimized px-6">
          <motion.div
            className="grid gap-12 lg:grid-cols-2 lg:gap-16"
            initial="hidden"
            animate="visible"
            variants={{
              hidden: {},
              visible: {
                transition: { staggerChildren: 0.15, delayChildren: 0.2 },
              },
            }}
          >
            {/* Left Column */}
            <div className="space-y-6 text-center lg:text-left">
              <motion.h1
                className="text-display-hero font-black text-white"
                variants={{
                  hidden: { opacity: 0, y: 20 },
                  visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
                }}
              >
                Oncology,{" "}
                <span className="bg-gradient-to-r from-cyan-400 to-violet-400 bg-clip-text text-transparent">
                  unified
                </span>{" "}
                in 60 seconds
              </motion.h1>

              <motion.p
                className="text-body-large mx-auto max-w-xl text-slate-300 lg:mx-0"
                variants={{
                  hidden: { opacity: 0, y: 20 },
                  visible: {
                    opacity: 1,
                    y: 0,
                    transition: { duration: 0.6, delay: 0.1 },
                  },
                }}
              >
                Unifies EMR, PACS, and LIS into an ABDM-compliant Patient OS
                with AI summaries, ready for tumor board decisions.
              </motion.p>

              <motion.div
                className="flex flex-col gap-4 sm:flex-row sm:justify-center lg:justify-start"
                variants={{
                  hidden: { opacity: 0, y: 10 },
                  visible: {
                    opacity: 1,
                    y: 0,
                    transition: { duration: 0.6, delay: 0.2 },
                  },
                }}
              >
                <Button
                  className="bg-gradient-to-r from-cyan-500 to-blue-500 text-white shadow-lg hover:shadow-xl"
                  size="lg"
                >
                  Book a Demo <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
                <Button
                  className="border-white/20 text-white hover:border-white/40 hover:bg-white/10"
                  size="lg"
                >
                  <Play className="mr-2 h-4 w-4" /> Watch Prototype
                </Button>
              </motion.div>

              <motion.div className="flex flex-wrap justify-center gap-3 lg:justify-start">
                {badges.map((badge) => (
                  <Badge
                    key={badge.text}
                    variant="outline"
                    className="border-white/20 bg-white/5 px-3 py-1.5 text-white backdrop-blur-sm"
                  >
                    <badge.icon className="mr-2 h-3.5 w-3.5" /> {badge.text}
                  </Badge>
                ))}
              </motion.div>
            </div>

            {/* Right Column - Animated Cards */}
            <motion.div className="relative space-y-4">
              {workflowSteps.map((step) => (
                <AnimatedCard key={step.title} {...step} />
              ))}
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
});

EntheoryHeroShader.displayName = "EntheoryHeroShader";

export default EntheoryHeroShader;
