"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import React, { memo, useEffect, useMemo, useRef, useState } from "react";

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

export interface ScrollytellingFrame {
  id: string;
  headline: React.ReactNode;
  description: string;
  image: string;
  alt: string;
  showCTA?: boolean;
}

const frames: ScrollytellingFrame[] = [
  {
    id: "frame-1",
    headline: (
      <>
        Stop chasing. <br />
        <em className="font-bold italic text-primary">Start seeing.</em>
      </>
    ),
    description: "Auto‑fetch with consent...",
    image: "/media/section5/frame-a-rivers.png",
    alt: "Data rivers",
  },
  {
    id: "frame-2",
    headline: (
      <>
        Standards in. <br />
        <em className="font-bold italic text-primary">Standards out.</em>
      </>
    ),
    description: "FHIR bundles & DICOM links...",
    image: "/media/section5/frame-b-mycelium.png",
    alt: "Standards connecting",
  },
  {
    id: "frame-3",
    headline: (
      <>
        Care, <br />
        <em className="font-bold italic text-primary">in motion.</em>
      </>
    ),
    description: "One-click orders, pre-filled...",
    image: "/media/section5/frame-c-fireflies.png",
    alt: "Care path",
  },
  {
    id: "frame-4",
    headline: (
      <>
        Where connection <br />
        <em className="font-bold italic text-primary">is infinite.</em>
      </>
    ),
    description: "The oncology OS for interoperable, efficient care.",
    image: "/media/section5/frame-d-emblem.png",
    alt: "Entheory emblem",
    showCTA: true,
  },
];

const handlePilotClick = () => {
  window.dispatchEvent(new Event("early-pilot-open"));
};

const ScrollytellingSection: React.FC = memo(() => {
  const sectionRef = useRef<HTMLElement>(null);
  const [imagesLoaded, setImagesLoaded] = useState(false);

  const imagePromises = useMemo(
    () =>
      frames.map((frame) => {
        return new Promise<void>((resolve, reject) => {
          const img = new Image();
          img.src = frame.image;
          img.onload = () => resolve();
          img.onerror = () => reject(`Failed to load ${frame.image}`);
        });
      }),
    []
  );

  useEffect(() => {
    Promise.all(imagePromises)
      .then(() => setImagesLoaded(true))
      .catch(console.error);
  }, [imagePromises]);

  useEffect(() => {
    if (!imagesLoaded || !sectionRef.current) return;

    let pinTrigger: ScrollTrigger | null = null;
    let timeline: gsap.core.Timeline | null = null;

    try {
      const frameElements =
        gsap.utils.toArray<HTMLDivElement>(".scrolly-frame");

      pinTrigger = ScrollTrigger.create({
        trigger: sectionRef.current,
        start: "top top",
        end: `+=${frames.length * 100}%`,
        pin: true,
        pinSpacing: true,
      });

      timeline = gsap.timeline({
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top top",
          end: `+=${frames.length * 100}%`,
          scrub: 1,
        },
      });

      frameElements.forEach((frame, i) => {
        if (i > 0) {
          timeline!.from(
            frame,
            { autoAlpha: 0, scale: 1.05, y: 50 },
            `frame-${i}`
          );

          timeline!.to(
            frameElements[i - 1],
            { autoAlpha: 0, scale: 0.95, y: -50 },
            `frame-${i}`
          );
        }

        timeline!.to({}, { duration: 0.5 });
      });
    } catch (error) {
      console.error("GSAP animation error:", error);
    }

    return () => {
      try {
        pinTrigger?.kill();
        timeline?.scrollTrigger?.kill();
        timeline?.kill();
      } catch (error) {
        console.error("GSAP cleanup error:", error);
      }
    };
  }, [imagesLoaded]);

  return (
    <section
      ref={sectionRef}
      className="relative h-screen w-full overflow-hidden"
    >
      {frames.map((frame, index) => (
        <article
          key={frame.id}
          className="scrolly-frame absolute inset-0 flex h-full w-full flex-col items-center justify-center p-8 lg:flex-row"
          style={{ visibility: index === 0 ? "visible" : "hidden" }}
        >
          {}
          <div className="flex w-full flex-col justify-center text-center lg:w-1/2 lg:text-left">
            <h2 className="text-display-hero leading-tight text-gray-900">
              {frame.headline}
            </h2>
            <p className="text-body-large mt-4 max-w-lg">{frame.description}</p>
            {frame.showCTA && (
              <div className="mt-6">
                <Button size="lg" onClick={handlePilotClick}>
                  Partner for an early pilot →
                </Button>
              </div>
            )}
          </div>

          {}
          <div className="flex w-full items-center justify-center lg:w-1/2">
            <img
              src={frame.image}
              alt={frame.alt}
              className="h-auto w-full max-w-md object-contain"
              style={{ filter: "drop-shadow(0 20px 40px rgba(0,0,0,0.1))" }}
            />
          </div>
        </article>
      ))}
      {!imagesLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/50 backdrop-blur-sm">
          <p>Loading visualizations...</p>
        </div>
      )}
    </section>
  );
});

ScrollytellingSection.displayName = "ScrollytellingSection";

export default ScrollytellingSection;
