import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { motion } from "framer-motion";
import { ArrowRight, CheckCircle2 } from "lucide-react";
import React, { memo, useCallback, useMemo, useState } from "react";

const FinalCTA: React.FC = memo(() => {
  const [email, setEmail] = useState("");
  const [submitted, setSubmitted] = useState(false);
  const [error, setError] = useState("");

  const onSubmit = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault();
      if (!/[^\s@]+@[^\s@]+\.[^\s@]+/.test(email)) {
        setError("Please enter a valid email address.");
        return;
      }
      setError("");
      console.log("Email submitted for demo:", email);
      setSubmitted(true);
    },
    [email]
  );

  const handleEmailChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setEmail(e.target.value);
      if (error) setError("");
    },
    [error]
  );

  const features = useMemo(
    () => [
      "Intake & consent in under a minute",
      "One unified timeline with full provenance",
      "Board‑ready summary with citations",
      "Automated safety checks & trial matching",
    ],
    []
  );

  return (
    <section className="overflow-hidden bg-slate-50">
      <div className="container-optimized py-16 md:py-24">
        <div className="rounded-2xl bg-gradient-to-br from-primary to-indigo-700 p-8 text-white md:p-12">
          <div className="grid items-center gap-8 lg:grid-cols-2 lg:gap-12">
            {/* Left Column: Content & Form */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ duration: 0.6, ease: "easeOut" }}
            >
              <div className="mb-2 text-sm font-semibold uppercase tracking-wider text-cyan-300">
                See It in Action
              </div>
              <h2 className="text-display-large font-bold text-white">
                Ready to unify oncology care?
              </h2>
              <p className="mt-4 max-w-lg text-lg leading-relaxed text-indigo-100">
                Get a personalized 15-minute walkthrough of the OncoConnect
                platform. No sales talk—just the product.
              </p>

              {!submitted ? (
                <form
                  onSubmit={onSubmit}
                  className="mt-8 flex flex-col gap-3 sm:flex-row sm:items-start"
                >
                  <div className="w-full sm:flex-grow">
                    <Input
                      id="cta-email"
                      type="email"
                      required
                      value={email}
                      onChange={handleEmailChange}
                      placeholder="Enter your work email"
                      className="h-12 border-indigo-300 bg-white/20 text-white placeholder:text-indigo-200 focus:bg-white/25 focus:ring-white"
                      aria-invalid={!!error}
                    />
                    {error && (
                      <p className="mt-2 text-sm text-yellow-300">{error}</p>
                    )}
                  </div>
                  <Button
                    type="submit"
                    size="lg"
                    className="h-12 bg-white text-primary shadow-lg hover:bg-slate-200"
                  >
                    Request Demo <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </form>
              ) : (
                <div className="mt-8 rounded-md bg-green-500/80 p-4 text-center font-medium">
                  Thanks! We’ll be in touch to schedule your demo.
                </div>
              )}
            </motion.div>

            {/* Right Column: Value Propositions */}
            <motion.div
              className="relative"
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
            >
              <div className="rounded-xl border border-white/20 bg-white/10 p-6 backdrop-blur-sm">
                <h4 className="mb-4 text-lg font-semibold text-white">
                  In your demo, you'll see how to:
                </h4>
                <ul className="space-y-3">
                  {features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <CheckCircle2 className="mt-1 h-5 w-5 flex-shrink-0 text-cyan-300" />
                      <span className="text-indigo-100">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
});

FinalCTA.displayName = "FinalCTA";

export default FinalCTA;
