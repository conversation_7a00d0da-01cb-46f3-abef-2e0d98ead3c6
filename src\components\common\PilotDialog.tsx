import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { postLead } from "@/lib/lead";
import React, { useCallback, useEffect, useState } from "react";

const PilotDialog: React.FC = () => {
  const [open, setOpen] = useState(false);
  const [email, setEmail] = useState("");
  const [fullName, setFullName] = useState("");
  const [role, setRole] = useState<"physician" | "administration">("physician");
  const [hospital, setHospital] = useState("");
  const [city, setCity] = useState("");
  const [phone, setPhone] = useState("");
  const [notes, setNotes] = useState("");
  const [submitted, setSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    const handler = () => setOpen(true);
    window.addEventListener("early-pilot-open", handler as EventListener);
    return () =>
      window.removeEventListener("early-pilot-open", handler as EventListener);
  }, []);

  const resetForm = useCallback(() => {
    setEmail("");
    setFullName("");
    setHospital("");
    setCity("");
    setPhone("");
    setNotes("");
    setRole("physician");
    setSubmitted(false);
  }, []);

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();
      if (!fullName.trim() || !email.trim() || !hospital.trim()) return;
      setIsSubmitting(true);
      try {
        await postLead({
          source: "pilot",
          email,
          name: fullName,
          role,
          hospital,
          city,
          phone,
          notes,
        });
        setSubmitted(true);
      } catch (error) {
        console.error("Failed to submit pilot form:", error);
      } finally {
        setIsSubmitting(false);
      }
    },
    [email, fullName, role, hospital, city, phone, notes]
  );

  return (
    <Dialog
      open={open}
      onOpenChange={(v) => {
        setOpen(v);
        if (!v) resetForm();
      }}
    >
      <DialogContent className="overflow-hidden p-0 sm:max-w-lg">
        <div className="p-6 md:p-8">
          {!submitted ? (
            <>
              <DialogHeader className="space-y-2">
                <DialogTitle className="text-2xl font-semibold">
                  Partner for an Early Pilot
                </DialogTitle>
                <DialogDescription>
                  Book a personalized walkthrough to explore a limited early
                  pilot with your oncology team.
                </DialogDescription>
              </DialogHeader>

              <form className="mt-6 grid gap-4" onSubmit={handleSubmit}>
                <div className="grid gap-4">
                  <Input
                    id="pilot-name-global"
                    type="text"
                    required
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    placeholder="Full name"
                  />
                  <Input
                    id="pilot-email-global"
                    type="email"
                    required
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Work email"
                  />
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <Input
                      id="pilot-hospital"
                      type="text"
                      required
                      value={hospital}
                      onChange={(e) => setHospital(e.target.value)}
                      placeholder="Hospital / Institution"
                    />
                    <Input
                      id="pilot-city"
                      type="text"
                      value={city}
                      onChange={(e) => setCity(e.target.value)}
                      placeholder="City"
                    />
                  </div>
                  <div className="space-y-2">
                    <span className="text-sm font-medium">Your role</span>
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant={role === "physician" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setRole("physician")}
                      >
                        Physician
                      </Button>
                      <Button
                        type="button"
                        variant={
                          role === "administration" ? "default" : "outline"
                        }
                        size="sm"
                        onClick={() => setRole("administration")}
                      >
                        Administration
                      </Button>
                    </div>
                  </div>
                  <textarea
                    id="pilot-notes"
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    placeholder="Key context (e.g., tumor board workflow, EHR vendors)"
                    className="min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  />
                  <Button type="submit" size="lg" disabled={isSubmitting}>
                    {isSubmitting ? "Submitting..." : "Book Early Pilot"}
                  </Button>
                </div>
                <p className="mt-2 text-xs text-muted-foreground">
                  By submitting, you agree to be contacted. No spam.
                </p>
              </form>
            </>
          ) : (
            <div className="py-12 text-center">
              <h3 className="text-2xl font-semibold text-foreground">
                Thank you!
              </h3>
              <p className="mt-2 text-muted-foreground">
                We've received your request and will be in touch shortly.
              </p>
              <div className="mt-6">
                <Button
                  onClick={() => {
                    setOpen(false);
                    resetForm();
                  }}
                >
                  Close
                </Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PilotDialog;
