import { motion } from "framer-motion";
import {
  ArrowLeft,
  Check,
  ChevronDown,
  ChevronUp,
  Database,
  Lock,
  Shield,
  ShieldCheck,
} from "lucide-react";
import React, { memo, useCallback, useMemo, useState } from "react";
import { Link, useParams } from "react-router-dom";

const TITLES: Record<string, string> = {
  consent: "Consent-First Data Model",
  provenance: "Data Provenance & Traceability",
  audit: "Comprehensive Auditing & RBAC",
  standards: "Adherence to Industry Standards",
};

const pageVariants = {
  hidden: { opacity: 0, y: 12 },
  enter: { opacity: 1, y: 0 },
};

const heroVariants = {
  hidden: { opacity: 0, y: 8 },
  enter: { opacity: 1, y: 0 },
};

const cardVariants = {
  hidden: { opacity: 0, y: 10, scale: 0.995 },
  enter: (i: number) => ({
    opacity: 1,
    y: 0,
    scale: 1,
    transition: { delay: 0.08 * i },
  }),
};

const TrustExplainer: React.FC = memo(() => {
  const { slug } = useParams<{ slug: string }>();
  const title = useMemo(
    () => TITLES[slug || ""] || "Our Commitment to Trust",
    [slug]
  );
  const [openIndex, setOpenIndex] = useState<number | null>(0);

  const features = useMemo(
    () => [
      {
        title: "Encryption & Data Safety",
        body: "All data is encrypted in transit and at rest with industry-standard ciphers and key management. We support BYOK and HSM integrations.",
        icon: Lock,
      },
      {
        title: "Provenance & Versioning",
        body: "Every dataset and model prediction includes an auditable provenance record — timestamps, model version, and contributing data sources.",
        icon: Database,
      },
      {
        title: "Fine-Grained Access Control",
        body: "Role-Based Access Control (RBAC), attribute-based policies, and scoped API keys let you enforce least-privilege across integrations.",
        icon: Shield,
      },
    ],
    []
  );

  const stats = useMemo(
    () => [
      { label: "Clinical Trials Supported", value: "120+" },
      { label: "Third-party Audits", value: "8" },
      { label: "Avg. Time-to-Integrate", value: "3–6 weeks" },
    ],
    []
  );

  const toggleAccordion = useCallback(
    (i: number) => setOpenIndex((prev) => (prev === i ? null : i)),
    []
  );

  return (
    <motion.div
      className="bg-neutral-50 text-neutral-900 dark:bg-neutral-900 dark:text-neutral-100"
      initial="hidden"
      animate="enter"
      variants={pageVariants}
      transition={{ duration: 0.45 }}
    >
      <div className="mx-auto max-w-5xl px-6 py-12">
        {/* Back + small header */}
        <motion.div
          className="mb-8 flex items-center gap-4"
          variants={heroVariants}
          transition={{ duration: 0.45 }}
        >
          <Link
            to="/"
            className="inline-flex items-center gap-2 text-sm font-medium text-slate-700 hover:text-slate-900 dark:text-slate-300"
            aria-label="Back to home"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to home
          </Link>
        </motion.div>

        {/* Hero */}
        <motion.header
          className="mb-10 grid gap-6 md:grid-cols-3 md:items-center"
          variants={heroVariants}
          transition={{ duration: 0.5 }}
          aria-labelledby="trust-title"
        >
          <div className="md:col-span-2">
            <div className="flex items-center gap-4">
              <div className="rounded-full bg-gradient-to-br from-emerald-50 to-cyan-50 p-3 dark:from-emerald-900 dark:to-cyan-950">
                <ShieldCheck className="h-8 w-8 text-emerald-600 dark:text-emerald-300" />
              </div>
              <div>
                <h1
                  id="trust-title"
                  className="text-2xl font-extrabold leading-tight md:text-4xl"
                >
                  {title}
                </h1>
                <p className="mt-2 max-w-xl text-sm text-muted-foreground">
                  Clear, auditable, and secure systems designed for clinical
                  workflows. We prioritize explainability and control so that
                  clinicians and administrators can trust every AI-driven
                  insight.
                </p>
              </div>
            </div>
          </div>

          {/* CTA / quick facts */}
          <div className="order-first md:order-last md:col-span-1">
            <div className="rounded-xl border border-slate-100 bg-white p-4 shadow-sm dark:border-neutral-800 dark:bg-neutral-800">
              <dl className="space-y-3">
                {stats.map((s) => (
                  <div key={s.label} className="flex justify-between">
                    <dt className="text-xs text-muted-foreground">{s.label}</dt>
                    <dd className="text-sm font-semibold">{s.value}</dd>
                  </div>
                ))}
              </dl>

              <div className="mt-4 flex gap-3">
                <a
                  href="#contact"
                  className="inline-flex items-center justify-center rounded-md bg-emerald-600 px-3 py-2 text-sm font-medium text-white shadow-sm hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500"
                >
                  Request audit
                </a>
                <a
                  href="#download"
                  className="inline-flex items-center justify-center rounded-md border border-slate-200 px-3 py-2 text-sm font-medium text-slate-700 hover:bg-slate-50"
                >
                  Download whitepaper
                </a>
              </div>
            </div>
          </div>
        </motion.header>

        {/* Features grid */}
        <section className="mb-12 grid gap-6 md:grid-cols-3">
          {features.map((f, i) => {
            const Icon = f.icon;
            return (
              <motion.article
                key={f.title}
                className="rounded-2xl border border-slate-100 bg-white p-6 shadow-sm dark:border-neutral-800 dark:bg-neutral-800"
                custom={i}
                initial="hidden"
                animate="enter"
                variants={cardVariants}
              >
                <div className="flex items-start gap-4">
                  <div className="rounded-lg bg-emerald-50 p-3 dark:bg-emerald-900">
                    <Icon className="h-6 w-6 text-emerald-600 dark:text-emerald-300" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">{f.title}</h3>
                    <p className="mt-2 text-sm text-muted-foreground">
                      {f.body}
                    </p>
                  </div>
                </div>
              </motion.article>
            );
          })}
        </section>

        {/* Deep dive + accordion */}
        <div className="mb-16 grid gap-8 md:grid-cols-2">
          <motion.div
            className="prose dark:prose-invert max-w-none"
            initial={{ opacity: 0, x: -8 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.55 }}
          >
            <h2>Why trust matters</h2>
            <p className="lead">
              In clinical settings trust is not optional. Our engineering and
              governance approach blends cryptographic guarantees, auditable
              pipelines, and human-in-the-loop controls so clinicians can make
              informed decisions quickly and confidently.
            </p>

            <h3 className="mt-6">How we operationalize trust</h3>
            <ul>
              <li>Explainable model outputs with confidence intervals</li>
              <li>Immutable provenance logs for each prediction</li>
              <li>Granular audit trails and role-based access</li>
            </ul>
          </motion.div>

          <div>
            <h3 className="mb-4 text-lg font-semibold">
              Implementation details
            </h3>

            <div className="space-y-3">
              {[
                {
                  q: "How is data provenance recorded?",
                  a: "We record dataset hashes, ingestion timestamps, transformation steps, and model versions. Records are stored in an append-only ledger to ensure tamper evidence.",
                },
                {
                  q: "Who can access audit logs?",
                  a: "Access is governed by RBAC and audit policies. Only authorized roles (security leads, compliance officers) can view full logs; others can view redacted summaries.",
                },
                {
                  q: "How often are models audited?",
                  a: "We run scheduled automated tests and quarterly third-party assessments. Any model updates include a retraining report and validation summary.",
                },
              ].map((item, i) => (
                <div
                  key={item.q}
                  className="rounded-lg border border-slate-100 bg-white p-4 dark:border-neutral-800 dark:bg-neutral-800"
                >
                  <button
                    onClick={() => toggleAccordion(i)}
                    aria-expanded={openIndex === i}
                    aria-controls={`acc-${i}`}
                    className="flex w-full items-center justify-between gap-3 text-left"
                  >
                    <span className="font-medium">{item.q}</span>
                    <span className="text-muted-foreground">
                      {openIndex === i ? (
                        <ChevronUp className="h-4 w-4" aria-hidden />
                      ) : (
                        <ChevronDown className="h-4 w-4" aria-hidden />
                      )}
                    </span>
                  </button>

                  <div
                    id={`acc-${i}`}
                    role="region"
                    aria-hidden={openIndex !== i}
                    className={`mt-3 overflow-hidden transition-all duration-300 ${
                      openIndex === i ? "max-h-96" : "max-h-0"
                    }`}
                  >
                    <p className="text-sm text-muted-foreground">{item.a}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* CTA */}
        <motion.section
          className="rounded-2xl bg-gradient-to-r from-emerald-600 to-cyan-500 p-8 text-white"
          initial={{ opacity: 0, y: 8 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex flex-col items-start gap-4 md:flex-row md:items-center md:justify-between">
            <div>
              <h3 className="text-xl font-semibold">
                Ready to evaluate with clinical rigor?
              </h3>
              <p className="mt-1 text-sm opacity-90">
                Request a tailored security & validation plan for your
                deployment.
              </p>
            </div>

            <div className="mt-4 flex gap-3 md:mt-0">
              <a
                href="#request"
                className="inline-flex items-center gap-2 rounded-md bg-white/10 px-4 py-2 text-sm font-medium shadow-sm hover:bg-white/20"
              >
                <Check className="h-4 w-4" />
                Request evaluation
              </a>

              <a
                href="#contact"
                className="inline-flex items-center gap-2 rounded-md bg-white px-4 py-2 text-sm font-semibold text-emerald-700"
              >
                Contact sales
              </a>
            </div>
          </div>
        </motion.section>
      </div>
    </motion.div>
  );
});

TrustExplainer.displayName = "TrustExplainer";

export default TrustExplainer;
