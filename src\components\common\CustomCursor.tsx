import { motion, useMotionValue, useSpring } from "framer-motion";
import React, { useCallback, useEffect, useState } from "react";

const TRAIL_LENGTH = 8;

const CustomCursor: React.FC = () => {
  const [isPointer, setIsPointer] = useState(false);
  const cursorX = useMotionValue(-100);
  const cursorY = useMotionValue(-100);

  const springConfig = { damping: 25, stiffness: 700, mass: 0.1 };
  const trailX = Array.from({ length: TRAIL_LENGTH }, () =>
    useSpring(cursorX, springConfig)
  );
  const trailY = Array.from({ length: TRAIL_LENGTH }, () =>
    useSpring(cursorY, springConfig)
  );

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      cursorX.set(e.clientX);
      cursorY.set(e.clientY);

      const target = e.target as HTMLElement;
      const isPointerElement = !!target.closest(
        "a, button, [data-cursor='pointer']"
      );
      setIsPointer(isPointerElement);
    },
    [cursorX, cursorY, isPointer]
  );

  useEffect(() => {
    document.addEventListener("mousemove", handleMouseMove);
    document.body.style.cursor = "none";

    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.body.style.cursor = "auto";
    };
  }, [handleMouseMove]);

  return (
    <>
      {[...Array(TRAIL_LENGTH)].map((_, i) => (
        <motion.div
          key={i}
          className="pointer-events-none fixed z-[9998] rounded-full"
          style={{
            left: trailX[i],
            top: trailY[i],
            x: "-50%",
            y: "-50%",
            scale: (TRAIL_LENGTH - i) / TRAIL_LENGTH,
            opacity: (TRAIL_LENGTH - i) / (TRAIL_LENGTH * 1.5),
            background: isPointer
              ? "radial-gradient(circle, hsl(var(--primary)) 20%, transparent 100%)"
              : "radial-gradient(circle, hsl(var(--brand-primary-light)) 20%, transparent 100%)",
          }}
          animate={{
            width: isPointer ? 40 : 20,
            height: isPointer ? 40 : 20,
            transition: { type: "spring", stiffness: 500, damping: 30 },
          }}
        />
      ))}
      <motion.div
        className="pointer-events-none fixed z-[9999] rounded-full"
        style={{
          left: cursorX,
          top: cursorY,
          x: "-50%",
          y: "-50%",
        }}
        animate={{
          width: isPointer ? 12 : 8,
          height: isPointer ? 12 : 8,
          borderWidth: isPointer ? "4px" : "0px",
          borderColor: "hsl(var(--primary-foreground))",
          backgroundColor: isPointer
            ? "hsl(var(--primary))"
            : "hsl(var(--primary-foreground))",
        }}
        transition={{ type: "spring", stiffness: 700, damping: 30 }}
      />
    </>
  );
};

export default CustomCursor;
