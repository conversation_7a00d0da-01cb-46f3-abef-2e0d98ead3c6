import { motion } from "framer-motion";
import { Brain, Clock, Cloud, Database, Monitor, Shield } from "lucide-react";
import React, { memo } from "react";

interface Feature {
  icon: React.ReactNode;
  title: string;
  description: string;
  highlight: string;
  metric: string;
}

const Features: React.FC = memo(() => {
  const features: Feature[] = [
    {
      icon: <Database className="h-6 w-6 text-primary" />,
      title: "Data Aggregation",
      description:
        "Seamless integration of EMR, PACS, LIS, HIMS, pathology, billing, and telemedicine sources.",
      highlight: "7+ Sources",
      metric: "99.9% Uptime",
    },
    {
      icon: <Clock className="h-6 w-6 text-accent" />,
      title: "Longitudinal Profiles",
      description:
        "Chronological patient timelines from multi-source inputs for comprehensive care context.",
      highlight: "Real-time",
      metric: "< 2s Response",
    },
    {
      icon: <Brain className="h-6 w-6 text-success" />,
      title: "AI-Generated Reports",
      description:
        "Contextual analysis with grounded citations from PubMed, ClinicalTrials.gov, and other databases.",
      highlight: "Evidence-Based",
      metric: "95% Accuracy",
    },
  ];

  const additionalFeatures: Feature[] = [
    {
      icon: <Monitor className="h-6 w-6 text-primary" />,
      title: "Intuitive Dashboard",
      description:
        "Modern interface designed for tumor boards with query-based evidence retrieval capabilities.",
      highlight: "User-Friendly",
      metric: "4.9/5 Rating",
    },
    {
      icon: <Cloud className="h-6 w-6 text-accent" />,
      title: "Enterprise Scalability",
      description:
        "Cloud deployment for enterprises with ABDM-compliant APIs and flexible architecture.",
      highlight: "ABDM Ready",
      metric: "Auto-scaling",
    },
    {
      icon: <Shield className="h-6 w-6 text-success" />,
      title: "Full Compliance",
      description:
        "Complete adherence to DPDP Act 2023, ABDM guidelines, FHIR standards, and international regulations.",
      highlight: "Certified",
      metric: "SOC 2 Type II",
    },
  ];

  return (
    <section id="features" className="bg-gradient-subtle py-20">
      <div className="container-optimized space-y-20">
        {/* Section Heading */}
        <motion.div
          className="space-y-4 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <div className="inline-flex items-center gap-2 rounded-full bg-orange-300 px-4 py-2 text-sm font-medium text-orange-900">
            <div className="h-2 w-2 animate-pulse rounded-full bg-orange-900"></div>
            Platform Features
          </div>
          <h2 className="lg:text-display-large mb-4 text-heading-1 text-foreground">
            Comprehensive Healthcare
            <span className="block text-primary">Interoperability</span>
          </h2>
          <p className="mx-auto max-w-3xl text-xl text-muted-foreground">
            Our platform combines advanced AI with healthcare-grade security and
            compliance to deliver seamless data integration and evidence-based
            insights.
          </p>
        </motion.div>

        {/* Primary Features */}
        <div className="mb-16 grid gap-8 lg:grid-cols-3">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              className="group relative h-full rounded-2xl border border-border/50 bg-background p-6 shadow-sm transition-all duration-300 hover:border-border hover:shadow-lg"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.15 }}
            >
              <div className="mb-6 flex items-start justify-between">
                <div className="flex h-12 w-12 items-center justify-center rounded-xl border border-border/20 bg-background shadow-sm transition-transform duration-200 group-hover:scale-105">
                  {feature.icon}
                </div>
                <div className="text-right">
                  <div className="mb-1 rounded-full bg-primary px-3 py-1 text-xs font-medium text-primary-foreground">
                    {feature.highlight}
                  </div>
                  <div className="text-xs font-medium text-muted-foreground">
                    {feature.metric}
                  </div>
                </div>
              </div>
              <h3 className="mb-2 text-heading-3 text-foreground">
                {feature.title}
              </h3>
              <p className="text-body-base text-muted-foreground">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </div>

        {/* Secondary Features */}
        <div className="mb-16 grid items-center gap-12 lg:grid-cols-2">
          {/* Diagram */}
          <div className="relative">
            <img
              src="/media/interoperability-diagram.jpg"
              alt="Healthcare data interoperability platform"
              className="w-full rounded-3xl border border-border/20 shadow-lg"
            />
            <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-primary/10 via-transparent to-accent/10"></div>

            {/* Floating Metric */}
            <motion.div
              className="absolute right-6 top-6 rounded-2xl border border-border/20 bg-gradient-to-br from-primary/20 via-transparent to-accent/20 p-4 shadow-lg backdrop-blur-sm"
              initial={{ scale: 0.8, opacity: 0 }}
              whileInView={{ scale: 1, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              <div className="text-sm font-medium text-muted-foreground">
                Processing Speed
              </div>
              <div className="text-xl font-bold text-foreground">&lt; 2s</div>
              <div className="text-xs font-semibold text-success">
                Real-time
              </div>
            </motion.div>
          </div>

          {/* Additional Features */}
          <div className="space-y-8">
            {additionalFeatures.map((feature, index) => (
              <motion.div
                key={index}
                className="group flex items-start space-x-6"
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.15 }}
              >
                <div className="flex h-16 w-16 items-center justify-center rounded-2xl border border-border/20 bg-gradient-to-br from-background/50 to-background/10 shadow-md transition-transform duration-300 group-hover:scale-110">
                  {feature.icon}
                </div>
                <div className="flex-1">
                  <div className="mb-2 flex items-center justify-between gap-2">
                    <h3 className="text-xl font-semibold text-foreground">
                      {feature.title}
                    </h3>
                    <div className="flex items-center gap-2">
                      <span className="bg-gradient-accent rounded-full px-2 py-1 text-xs font-bold text-white">
                        {feature.highlight}
                      </span>
                      <span className="text-xs font-medium text-muted-foreground">
                        {feature.metric}
                      </span>
                    </div>
                  </div>
                  <p className="text-body-base leading-relaxed text-muted-foreground">
                    {feature.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
});

Features.displayName = "Features";

export default Features;
