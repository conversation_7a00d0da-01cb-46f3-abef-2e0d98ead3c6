import { Toaster as Sonner } from "@/components/ui/sonner";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import GSAPManager from "@/lib/gsapManager";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { AnimatePresence, motion } from "framer-motion";
import { lazy, memo, Suspense, useEffect } from "react";
import { BrowserRouter, Route, Routes, useLocation } from "react-router-dom";
import ErrorBoundary from "./components/common/ErrorBoundary";
import { PageLoadingState } from "./components/common/LoadingStates";
import LogoCustomCursor from "./components/common/LogoCustomCursor";
import PilotDialog from "./components/common/PilotDialog";
import { pageTransition, pageVariants } from "./lib/animations";

const Index = lazy(() => import("@/pages/Index"));
const TrustExplainer = lazy(() => import("@/pages/TrustExplainer"));
const NotFound = lazy(() => import("@/pages/NotFound"));

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5,
      gcTime: 1000 * 60 * 10,
      retry: (failureCount) => failureCount < 2,
      refetchOnWindowFocus: false,
      refetchOnReconnect: "always",
      refetchOnMount: true,
      networkMode: "online",
    },
    mutations: {
      retry: 1,
      networkMode: "online",
    },
  },
});

const AnimatedRoute = memo(({ children }: { children: React.ReactNode }) => {
  const location = useLocation();
  useEffect(() => {
    return () => {
      setTimeout(() => {
        GSAPManager.refresh();
      }, 300);
    };
  }, [location.pathname]);

  return (
    <motion.div
      variants={pageVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      transition={pageTransition}
      style={{ willChange: "transform, opacity" }}
    >
      {children}
    </motion.div>
  );
});

AnimatedRoute.displayName = "AnimatedRoute";

const AnimatedRoutes = memo(() => {
  const location = useLocation();
  return (
    <AnimatePresence mode="wait" onExitComplete={() => GSAPManager.refresh()}>
      <Routes location={location} key={location.pathname}>
        <Route
          path="/"
          element={
            <AnimatedRoute>
              <Index />
            </AnimatedRoute>
          }
        />
        <Route
          path="/trust"
          element={
            <AnimatedRoute>
              <TrustExplainer />
            </AnimatedRoute>
          }
        />
        <Route
          path="*"
          element={
            <AnimatedRoute>
              <NotFound />
            </AnimatedRoute>
          }
        />
      </Routes>
    </AnimatePresence>
  );
});

AnimatedRoutes.displayName = "AnimatedRoutes";

const App = () => {
  useEffect(() => {
    const preloadImages = [
      "/logo.png",
      "/media/aura/aura-bg-desktop.png",
      "/media/hero/hero-bg-desktop.png",
      "/media/security-shield.png",
      "/media/tumor-board.jpg",
      "/media/interoperability-diagram.jpg",
      "/media/hero-healthcare.jpg",
      "/media/section5/frame-a-rivers.png",
      "/media/section5/frame-b-mycelium.png",
      "/media/section5/frame-c-fireflies.png",
      "/media/section5/frame-d-emblem.png",
    ];
    preloadImages.forEach((src) => {
      const img = new Image();
      img.src = src;
    });
    GSAPManager.refresh();
    return () => {
      GSAPManager.cleanAll();
    };
  }, []);

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <TooltipProvider delayDuration={300}>
          <LogoCustomCursor />
          <Toaster />
          <Sonner
            position="bottom-right"
            toastOptions={{
              style: {
                background: "rgba(255, 255, 255, 0.95)",
                backdropFilter: "blur(8px)",
              },
            }}
          />
          <PilotDialog />
          <BrowserRouter>
            <a href="#main-content" className="skip-to-main">
              Skip to main content
            </a>
            <main id="main-content">
              <Suspense
                fallback={
                  <PageLoadingState
                    logoSrc="/logo.png"
                    subtitle="Oncology, unified in 60 seconds"
                  />
                }
              >
                <AnimatedRoutes />
              </Suspense>
            </main>
          </BrowserRouter>
        </TooltipProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default App;
