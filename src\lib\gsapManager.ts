import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

class GSAPManager {
  private static instance: GSAPManager;
  private isInitialized = false;
  private contexts = new Map<string, gsap.Context>();

  private constructor() {
    if (typeof window !== "undefined") {
      // Register all GSAP plugins centrally
      gsap.registerPlugin(ScrollTrigger);

      // Set global GSAP defaults for better performance
      gsap.defaults({
        ease: "power2.out",
        duration: 0.6,
      });

      // Configure ScrollTrigger defaults
      ScrollTrigger.defaults({
        toggleActions: "play none none reverse",
        scroller: window,
      });

      this.isInitialized = true;
    }
  }

  static getInstance(): GSAPManager {
    if (!GSAPManager.instance) {
      GSAPManager.instance = new GSAPManager();
    }
    return GSAPManager.instance;
  }

  createContext(id: string, element: HTMLElement): gsap.Context {
    // Clean existing context if it exists
    if (this.contexts.has(id)) {
      this.contexts.get(id)?.revert();
    }

    const ctx = gsap.context(() => {}, element);
    this.contexts.set(id, ctx);
    return ctx;
  }

  // Create a context with automatic cleanup
  createAutoContext(
    id: string,
    element: HTMLElement,
    callback: () => void
  ): gsap.Context {
    const ctx = this.createContext(id, element);
    ctx.add(callback);
    return ctx;
  }

  // Clean specific context
  cleanContext(id: string): void {
    const ctx = this.contexts.get(id);
    if (ctx) {
      ctx.revert();
      this.contexts.delete(id);
    }
  }

  // Global cleanup (use sparingly)
  cleanAll(): void {
    this.contexts.forEach((ctx) => ctx.revert());
    this.contexts.clear();
    ScrollTrigger.getAll().forEach((st) => st.kill());
  }

  // Refresh ScrollTrigger (for layout changes)
  refresh(): void {
    if (this.isInitialized) {
      ScrollTrigger.refresh();
    }
  }

  // Batch refresh with debouncing for performance
  refreshDebounced = this.debounce(() => {
    this.refresh();
  }, 100);

  // Utility: Debounce function
  private debounce(func: Function, wait: number) {
    let timeout: NodeJS.Timeout;
    return (...args: any[]) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), wait);
    };
  }

  // Utility: Kill all ScrollTriggers for a specific element
  killScrollTriggersForElement(element: Element): void {
    ScrollTrigger.getAll().forEach((st) => {
      if (st.vars.trigger === element) {
        st.kill();
      }
    });
  }

  // Utility: Create a simple fade-in animation
  fadeIn(
    element: string | Element,
    options: gsap.TweenVars = {}
  ): gsap.core.Tween {
    return gsap.fromTo(
      element,
      { opacity: 0, y: 20 },
      { opacity: 1, y: 0, duration: 0.6, ease: "power2.out", ...options }
    );
  }

  // Utility: Create a simple fade-out animation
  fadeOut(
    element: string | Element,
    options: gsap.TweenVars = {}
  ): gsap.core.Tween {
    return gsap.to(element, {
      opacity: 0,
      duration: 0.4,
      ease: "power2.in",
      ...options,
    });
  }

  get gsap() {
    return gsap;
  }

  get ScrollTrigger() {
    return ScrollTrigger;
  }
}

export default GSAPManager.getInstance();
