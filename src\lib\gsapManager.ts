import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

class GSAPManager {
  private static instance: GSAPManager;
  private isInitialized = false;
  private contexts = new Map<string, gsap.Context>();

  private constructor() {
    if (typeof window !== "undefined") {
      gsap.registerPlugin(ScrollTrigger);
      this.isInitialized = true;
    }
  }

  static getInstance(): GSAPManager {
    if (!GSAPManager.instance) {
      GSAPManager.instance = new GSAPManager();
    }
    return GSAPManager.instance;
  }

  createContext(id: string, element: HTMLElement): gsap.Context {
    // Clean existing context if it exists
    if (this.contexts.has(id)) {
      this.contexts.get(id)?.revert();
    }

    const ctx = gsap.context(() => {}, element);
    this.contexts.set(id, ctx);
    return ctx;
  }

  // Clean specific context
  cleanContext(id: string): void {
    const ctx = this.contexts.get(id);
    if (ctx) {
      ctx.revert();
      this.contexts.delete(id);
    }
  }

  // Global cleanup (use sparingly)
  cleanAll(): void {
    this.contexts.forEach((ctx) => ctx.revert());
    this.contexts.clear();
    ScrollTrigger.getAll().forEach((st) => st.kill());
  }

  // Refresh ScrollTrigger (for layout changes)
  refresh(): void {
    if (this.isInitialized) {
      ScrollTrigger.refresh();
    }
  }

  get gsap() {
    return gsap;
  }

  get ScrollTrigger() {
    return ScrollTrigger;
  }
}

export default GSAPManager.getInstance();
