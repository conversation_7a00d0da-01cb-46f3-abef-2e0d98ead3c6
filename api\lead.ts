const ZOHO_BASE = process.env.ZOHO_BASE || "https://www.zohoapis.com";
const ZOHO_ACCOUNTS = process.env.ZOHO_ACCOUNTS || "https://accounts.zoho.com";
const ZOHO_CLIENT_ID = process.env.ZOHO_CLIENT_ID || "";
const ZOHO_CLIENT_SECRET = process.env.ZOHO_CLIENT_SECRET || "";
const ZOHO_REFRESH_TOKEN = process.env.ZOHO_REFRESH_TOKEN || "";
const APOLLO_API_KEY = process.env.APOLLO_API_KEY || "";

async function getZohoAccessToken() {
  const url = `${ZOHO_ACCOUNTS}/oauth/v2/token?refresh_token=${encodeURIComponent(ZOHO_REFRESH_TOKEN)}&client_id=${encodeURIComponent(ZOHO_CLIENT_ID)}&client_secret=${encodeURIComponent(ZOHO_CLIENT_SECRET)}&grant_type=refresh_token`;
  const res = await fetch(url, { method: "POST" });
  if (!res.ok) throw new Error("Zoho token error");
  const json = await res.json();
  return json.access_token as string;
}

async function createZohoLead(payload: any) {
  const token = await getZohoAccessToken();
  const data = {
    data: [
      {
        Last_Name: payload.name || "Prospect",
        Company: payload.hospital || "Unknown",
        Email: payload.email,
        Phone: payload.phone,
        City: payload.city,
        Lead_Source: payload.source === "pilot" ? "Early Pilot" : "Watch Demo",
        Description: payload.notes || "",
        Role__c: payload.role || "unknown",
      },
    ],
    trigger: ["approval", "workflow", "blueprint"],
  };
  const res = await fetch(`${ZOHO_BASE}/crm/v2/Leads`, {
    method: "POST",
    headers: {
      Authorization: `Zoho-oauthtoken ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });
  if (!res.ok) throw new Error("Zoho lead create failed");
  return res.json();
}

async function createApolloContact(payload: any) {
  if (!APOLLO_API_KEY) return { skipped: true };
  const res = await fetch("https://api.apollo.io/v1/contacts", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      api_key: APOLLO_API_KEY,
      contact: {
        first_name:
          payload.name?.split(" ").slice(0, -1).join(" ") || payload.name,
        last_name: payload.name?.split(" ").slice(-1).join(" ") || "Prospect",
        email: payload.email,
        organization_name: payload.hospital,
        city: payload.city,
        phone: payload.phone,
        tags: [payload.source === "pilot" ? "early-pilot" : "demo"],
      },
    }),
  });
  if (!res.ok) return { ok: false, status: res.status };
  return res.json();
}

// For Netlify

// import type { Handler } from "@netlify/functions";
// export const handler: Handler = async (event) => {
//   if (event.httpMethod !== "POST") {
//     return {
//       statusCode: 405,
//       body: JSON.stringify({ error: "Method not allowed" }),
//     };
//   }
//   try {
//     const body = JSON.parse(event.body || "{}");
//     const payload = {
//       source: body.source || "demo",
//       email: String(body.email || ""),
//       name: String(body.name || ""),
//       hospital: String(body.hospital || ""),
//       city: String(body.city || ""),
//       phone: String(body.phone || ""),
//       notes: String(body.notes || ""),
//       role: body.role || "unknown",
//     };
//     if (!payload.email)
//       return {
//         statusCode: 400,
//         body: JSON.stringify({ error: "Email required" }),
//       };

//     const [zoho, apollo] = await Promise.allSettled([
//       createZohoLead(payload),
//       createApolloContact(payload),
//     ]);
//     return {
//       statusCode: 200,
//       body: JSON.stringify({ ok: true, zoho, apollo }),
//     };
//   } catch (e: any) {
//     return {
//       statusCode: 500,
//       body: JSON.stringify({ error: e?.message || "Server error" }),
//     };
//   }
// };

// For Vercel

// import type { VercelRequest, VercelResponse } from "@vercel/node";
// export default async function handler(req: VercelRequest, res: VercelResponse) {
//   if (req.method !== "POST")
//     return res.status(405).json({ error: "Method not allowed" });
//   try {
//     const body = req.body || {};
//     const payload = {
//       source: body.source || "demo",
//       email: String(body.email || ""),
//       name: String(body.name || ""),
//       hospital: String(body.hospital || ""),
//       city: String(body.city || ""),
//       phone: String(body.phone || ""),
//       notes: String(body.notes || ""),
//       role: body.role || "unknown",
//     };
//     if (!payload.email)
//       return res.status(400).json({ error: "Email required" });

//     const [zoho, apollo] = await Promise.allSettled([
//       createZohoLead(payload),
//       createApolloContact(payload),
//     ]);
//     return res.status(200).json({ ok: true, zoho, apollo });
//   } catch (e: any) {
//     return res.status(500).json({ error: e?.message || "Server error" });
//   }
// }
