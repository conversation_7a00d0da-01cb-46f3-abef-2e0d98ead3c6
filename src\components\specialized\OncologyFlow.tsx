"use client";

import { motion, Variants } from "framer-motion";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Microscope,
  ShieldCheck,
  Stethoscope,
} from "lucide-react";
import React, { memo, useCallback, useMemo } from "react";

const BRANCH_OFFSET_CSS = "var(--branch-offset, 120px)";
const AXIS_X_CSS = "var(--hero-axis-x, 50vw)";
const CARD_WIDTH_CSS = "var(--card-w, 420px)";

type FlowCardItem = {
  id: string;
  k: string;
  title: string;
  chips: string[];
  gradient: string;
  icon: React.ReactNode;
  offset?: number;
  branch?: "left" | "right" | "center";
};

type FlowGateItem = {
  id: string;
  gate: "TRUE_FALSE_SPLIT";
};

type FlowItem = FlowCardItem | FlowGateItem;

interface OncologyFlowProps {
  className?: string;
  onCardClick?: (cardId: string) => void;
  enableAnimations?: boolean;
}

const IconGrad = memo(
  ({
    children,
    className = "",
  }: {
    children: React.ReactNode;
    className?: string;
  }) => (
    <div
      className={`grid h-7 w-7 flex-shrink-0 place-items-center rounded-full text-white shadow-inner shadow-black/20 ring-1 ring-white/40 ${className}`}
      aria-hidden
    >
      {children}
    </div>
  )
);
IconGrad.displayName = "IconGrad";

const Chip = memo(({ children }: { children: React.ReactNode }) => (
  <span className="rounded-md bg-white/80 px-1.5 py-0.5 text-[11px] font-medium text-slate-800 shadow-sm ring-1 ring-black/10">
    {children}
  </span>
));
Chip.displayName = "Chip";

const FlowCard = memo(
  ({
    k,
    title,
    chips,
    icon,
    gradient,
    onClick,
  }: {
    k: string;
    title: string;
    chips: string[];
    icon: React.ReactNode;
    gradient: string;
    onClick?: () => void;
  }) => {
    return (
      <div style={{ width: CARD_WIDTH_CSS }} className="relative mx-auto">
        <button
          onClick={onClick}
          aria-label={`${k}: ${title}`}
          className="w-full rounded-lg bg-white/95 p-3 shadow-[0_6px_20px_rgba(2,6,23,0.08)] ring-1 ring-black/5 backdrop-blur transition-all duration-200 hover:scale-[1.01] hover:shadow-[0_10px_28px_rgba(2,6,23,0.12)] focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-1"
        >
          <div className="flex items-start gap-2.5">
            <IconGrad className={`bg-gradient-to-br ${gradient}`}>
              {icon}
            </IconGrad>
            <div className="flex-1 text-left">
              <div className="text-[9px] font-semibold tracking-[0.12em] text-slate-400">
                {k}
              </div>
              <div className="mt-0.5 text-[13px] font-medium leading-tight text-slate-800">
                {title}
              </div>
              <div className="mt-1.5 flex flex-wrap gap-1">
                {chips.map((t) => (
                  <Chip key={t}>{t}</Chip>
                ))}
              </div>
            </div>
          </div>
        </button>
      </div>
    );
  }
);
FlowCard.displayName = "FlowCard";

const SplitGate = memo(() => {
  return (
    <div className="relative h-16" role="group" aria-label="Decision split">
      <div
        className="absolute left-1/2 top-0 h-1/2 w-px -translate-x-1/2 bg-slate-300/60"
        aria-hidden
      />
      <div
        className="absolute bottom-0 left-1/2 h-1/2 w-px -translate-x-1/2 bg-slate-300/25"
        aria-hidden
      />
      <div
        className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"
        aria-hidden
      >
        <div
          className="absolute left-0 top-0 h-px bg-slate-300/60"
          style={{ width: BRANCH_OFFSET_CSS }}
        />
        <div
          className="absolute right-0 top-0 h-px bg-slate-300/60"
          style={{ width: BRANCH_OFFSET_CSS }}
        />
      </div>
      <div
        className="absolute top-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full bg-slate-800/95 px-2.5 py-1 text-[10px] font-semibold tracking-wide text-white shadow-sm ring-1 ring-black/20"
        style={{ left: `calc(50% - 60px)` }}
        role="img"
        aria-label="True branch"
      >
        TRUE
      </div>
      <div
        className="absolute top-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full bg-slate-800/60 px-2.5 py-1 text-[10px] font-semibold tracking-wide text-white/90 shadow-sm ring-1 ring-black/10"
        style={{ left: `calc(50% + 60px)` }}
        role="img"
        aria-label="False branch"
      >
        FALSE
      </div>
    </div>
  );
});
SplitGate.displayName = "SplitGate";

const BranchRails = memo(({ axisX = AXIS_X_CSS }: { axisX?: string }) => {
  return (
    <>
      <div
        className="pointer-events-none absolute z-0 bg-slate-300/40"
        style={{
          left: `calc(${axisX} - ${BRANCH_OFFSET_CSS})`,
          top: 0,
          bottom: 0,
          width: 1,
        }}
        aria-hidden
      />
      <div
        className="pointer-events-none absolute z-0 bg-slate-300/40"
        style={{
          left: `calc(${axisX} + ${BRANCH_OFFSET_CSS})`,
          top: 0,
          bottom: 0,
          width: 1,
        }}
        aria-hidden
      />
    </>
  );
});
BranchRails.displayName = "BranchRails";

const ElbowFrom = memo(
  ({
    origin,
    branchOffset = BRANCH_OFFSET_CSS,
    cardOffset = 0,
  }: {
    origin: "left" | "right";
    branchOffset?: string;
    cardOffset?: number;
  }) => {
    const leftCalc =
      origin === "left"
        ? `calc(50% - ${branchOffset})`
        : `calc(50% + ${branchOffset})`;

    const connectionWidth =
      typeof window !== "undefined" && window.innerWidth < 768 ? 60 : 100;
    return (
      <div
        className="pointer-events-none absolute top-1/2 -translate-y-1/2"
        style={{
          left: leftCalc,
          width: connectionWidth,
          marginLeft: cardOffset ?? 0,
        }}
        aria-hidden
      >
        <div className="h-px w-full bg-slate-300/60" />
      </div>
    );
  }
);
ElbowFrom.displayName = "ElbowFrom";

const OncologyFlow: React.FC<OncologyFlowProps> = memo(
  ({ className = "", onCardClick, enableAnimations = true }) => {
    const getCardOffset = useCallback(() => {
      if (typeof window !== "undefined" && window.innerWidth < 768) return -100;
      return -240;
    }, []);

    const handleCardClick = useCallback(
      (cardId: string) => {
        onCardClick?.(cardId);
      },
      [onCardClick]
    );

    const steps: FlowItem[] = useMemo(
      () => [
        {
          id: "enroll",
          k: "PATIENT INTAKE & CONSENT",
          title: "Patient verified & consented",
          chips: ["ABDM linked", "Outside records attached"],
          icon: (
            <ClipboardCheck
              className="h-3.5 w-3.5"
              aria-hidden
              strokeWidth={2}
            />
          ),
          gradient: "from-cyan-400 via-blue-400 to-violet-400",
          branch: "center",
        },
        {
          id: "research",
          k: "RECONCILE RECORDS",
          title: "Unify history, labs & imaging into one view",
          chips: ["Latest regimen & lines", "Key labs & imaging summary"],
          icon: (
            <Stethoscope className="h-3.5 w-3.5" aria-hidden strokeWidth={2} />
          ),
          gradient: "from-fuchsia-400 via-violet-400 to-cyan-400",
          branch: "center",
        },
        { id: "gate", gate: "TRUE_FALSE_SPLIT" },
        {
          id: "enrich",
          k: "TUMOR BOARD PREP",
          title: "Clinical summary ready",
          chips: ["Stage & biomarkers", "Performance status"],
          icon: (
            <Microscope className="h-3.5 w-3.5" aria-hidden strokeWidth={2} />
          ),
          gradient: "from-indigo-400 via-blue-400 to-cyan-400",
          offset: getCardOffset(),
          branch: "left",
        },
        {
          id: "sequence",
          k: "PLAN & SAFETY",
          title: "Proposed regimen with checks",
          chips: ["DDI/allergy alerts", "Trial matches"],
          icon: (
            <ShieldCheck className="h-3.5 w-3.5" aria-hidden strokeWidth={2} />
          ),
          gradient: "from-pink-400 via-fuchsia-400 to-violet-400",
          offset: getCardOffset(),
          branch: "left",
        },
      ],
      [getCardOffset]
    );

    const itemVariants: Variants = {
      hidden: { opacity: 0, y: 12 },
      visible: (i: number) => ({
        opacity: 1,
        y: 0,
        transition: {
          duration: 0.35,
          ease: [0.22, 1, 0.36, 1],
          delay: i * 0.04,
        },
      }),
    };

    return (
      <section
        className={`relative isolate overflow-visible ${className}`}
        style={{ height: "var(--flow-h, 600px)", width: "100%" }}
        role="img"
        aria-label="Oncology workflow diagram"
      >
        <div
          className="absolute bottom-0 top-0 z-0 w-px bg-slate-300/45"
          style={{ left: AXIS_X_CSS }}
          aria-hidden
        />
        <div
          className="relative z-10"
          style={{
            position: "absolute",
            left: AXIS_X_CSS,
            top: "50%",
            transform: "translate(-50%, -50%)",
            width: "max-content",
          }}
        >
          <div className="mx-auto flex max-w-2xl flex-col gap-4">
            {steps.map((s, i) => {
              const isGate = (s as FlowGateItem).gate === "TRUE_FALSE_SPLIT";
              const card = s as FlowCardItem;
              const marginStyle =
                !isGate && card.offset !== undefined
                  ? { marginLeft: card.offset }
                  : undefined;

              return (
                <motion.div
                  key={s.id}
                  initial={enableAnimations ? "hidden" : undefined}
                  whileInView={enableAnimations ? "visible" : undefined}
                  viewport={{ once: true, amount: 0.6 }}
                  custom={i}
                  variants={itemVariants}
                  className="relative"
                  style={marginStyle}
                >
                  {isGate ? (
                    <SplitGate />
                  ) : (
                    <>
                      {card.branch === "left" && (
                        <ElbowFrom
                          origin="left"
                          branchOffset={BRANCH_OFFSET_CSS}
                          cardOffset={card.offset ?? 0}
                        />
                      )}
                      {card.branch === "right" && (
                        <ElbowFrom
                          origin="right"
                          branchOffset={BRANCH_OFFSET_CSS}
                          cardOffset={card.offset ?? 0}
                        />
                      )}
                      <FlowCard
                        k={card.k}
                        title={card.title}
                        chips={card.chips}
                        gradient={card.gradient}
                        icon={card.icon}
                        onClick={() => handleCardClick(card.id)}
                      />
                    </>
                  )}
                </motion.div>
              );
            })}
          </div>
        </div>
        <BranchRails axisX={AXIS_X_CSS} />
      </section>
    );
  }
);
OncologyFlow.displayName = "OncologyFlow";

export default OncologyFlow;
export type { OncologyFlowProps };
