"use client";

import GSAPManager from "@/lib/gsapManager";
import { motion } from "framer-motion";
import React, { memo, useEffect, useRef } from "react";

const SystemStandards: React.FC = memo(() => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const cloudRef = useRef<SVGSVGElement>(null);
  const threadsRef = useRef<SVGSVGElement>(null);
  const badgesRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!sectionRef.current) return;

    const ctx = GSAPManager.createAutoContext(
      "system-standards",
      sectionRef.current,
      () => {
        const tl = GSAPManager.gsap.timeline({
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top 80%",
            end: "bottom top",
            scrub: false,
          },
        });

        if (cloudRef.current) {
          tl.fromTo(
            cloudRef.current,
            { opacity: 0, scale: 0.8 },
            { opacity: 1, scale: 1, duration: 0.5, ease: "power1.out" },
            0
          );
        }

        if (threadsRef.current) {
          const paths = threadsRef.current.querySelectorAll(".thread-path");
          paths.forEach((path, i) => {
            const length = (path as SVGPathElement).getTotalLength();
            GSAPManager.gsap.set(path, {
              strokeDasharray: length,
              strokeDashoffset: length,
            });
            tl.to(
              path,
              { strokeDashoffset: 0, duration: 0.5, ease: "power2.inOut" },
              0.3 + i * 0.1
            );
          });
        }

        if (badgesRef.current) {
          const badgeElements = badgesRef.current.querySelectorAll(".badge");
          tl.fromTo(
            badgeElements,
            { opacity: 0, y: 20, scale: 0.8 },
            {
              opacity: 1,
              y: 0,
              scale: 1,
              duration: 0.4,
              stagger: 0.1,
              ease: "back.out(1.5)",
            },
            0.6
          );
        }
      }
    );

    return () => GSAPManager.cleanContext("system-standards");
  }, []);

  return (
    <section
      ref={sectionRef}
      className="relative min-h-screen overflow-hidden"
      style={{
        background:
          "linear-gradient(180deg, #faf7f2 0%, #f0f4ff 50%, #f6f7fb 100%)",
      }}
    >
      <div className="pointer-events-none absolute inset-0">
        <div
          className="absolute -inset-24 opacity-40"
          style={{
            background:
              "radial-gradient(ellipse at 30% 20%, rgba(147, 197, 253, 0.3), transparent 50%), radial-gradient(ellipse at 70% 80%, rgba(196, 181, 253, 0.3), transparent 50%)",
            animation: "pulse 10s ease-in-out infinite",
          }}
        />
      </div>

      <div className="container relative z-10 mx-auto grid min-h-screen items-center gap-12 px-6 md:px-16 lg:grid-cols-2">
        <div>
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-5xl font-light leading-tight text-slate-900 md:text-6xl lg:text-7xl"
          >
            Standards connect.
            <br />
            <em className="italic">Systems align.</em>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="mt-6 max-w-lg text-lg text-slate-600 md:text-xl"
          >
            Your data ecosystem unified through industry standards, creating
            seamless connections across every system.
          </motion.p>
        </div>

        <div className="relative flex h-full w-full items-center justify-center">
          <svg
            ref={cloudRef}
            className="absolute h-auto w-full max-w-md"
            viewBox="0 0 600 400"
          >
            <defs>
              <linearGradient
                id="cloudGradient"
                x1="0%"
                y1="0%"
                x2="100%"
                y2="100%"
              >
                <stop offset="0%" stopColor="#e0e7ff" />
                <stop offset="100%" stopColor="#cdd5f3" />
              </linearGradient>
            </defs>
            <ellipse
              cx="200"
              cy="200"
              rx="120"
              ry="60"
              fill="url(#cloudGradient)"
            />
            <ellipse
              cx="350"
              cy="180"
              rx="100"
              ry="55"
              fill="url(#cloudGradient)"
            />
            <ellipse
              cx="280"
              cy="220"
              rx="130"
              ry="50"
              fill="url(#cloudGradient)"
            />
          </svg>

          <svg
            ref={threadsRef}
            className="absolute h-full w-full max-w-md"
            viewBox="0 0 800 600"
          >
            <defs>
              <linearGradient
                id="threadGradient"
                x1="0%"
                y1="0%"
                x2="100%"
                y2="100%"
              >
                <stop offset="0%" stopColor="#a5b4fc" stopOpacity="0.6" />
                <stop offset="100%" stopColor="#818cf8" stopOpacity="0.3" />
              </linearGradient>
            </defs>
            <path
              className="thread-path"
              d="M 200 300 Q 300 250 400 280 T 600 320"
              stroke="url(#threadGradient)"
              strokeWidth="2"
              fill="none"
              strokeLinecap="round"
            />
            <path
              className="thread-path"
              d="M 150 350 Q 280 300 380 330 T 580 280"
              stroke="url(#threadGradient)"
              strokeWidth="2"
              fill="none"
              strokeLinecap="round"
            />
            <path
              className="thread-path"
              d="M 250 280 Q 350 320 450 300 T 620 350"
              stroke="url(#threadGradient)"
              strokeWidth="2"
              fill="none"
              strokeLinecap="round"
            />
            <path
              className="thread-path"
              d="M 180 320 Q 320 280 420 310 T 590 330"
              stroke="url(#threadGradient)"
              strokeWidth="2"
              fill="none"
              strokeLinecap="round"
            />
          </svg>

          <div
            ref={badgesRef}
            className="absolute inset-0 flex items-center justify-center"
          >
            <div className="relative h-full w-full max-w-md">
              {["ABDM", "FHIR R4", "DICOM", "HL7", "CSV"].map((text, i) => (
                <div
                  key={i}
                  className="badge absolute rounded-xl border border-slate-200 bg-white px-4 py-2 shadow-lg"
                  style={{
                    top: `${20 + i * 15}%`,
                    left: `${15 + i * 12}%`,
                  }}
                >
                  <span className="text-sm font-semibold text-slate-700">
                    {text}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
});

SystemStandards.displayName = "SystemStandards";

export default SystemStandards;
