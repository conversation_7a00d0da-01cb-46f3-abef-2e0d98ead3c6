import { supabase } from "@/integrations/supabase/client";

export interface PilotPayload {
  source: "pilot" | "demo";
  email: string;
  name?: string;
  role?: "physician" | "administration" | "unknown";
  hospital?: string;
  city?: string;
  phone?: string;
  notes?: string;
}

export async function postLead(payload: PilotPayload): Promise<Response> {
  // Method 1: Direct Supabase insert
  try {
    console.log("[LeadManager] Attempting Supabase insert...");
    const { data, error } = await supabase
      .from("leads")
      .insert(payload)
      .select();

    if (error) {
      throw new Error(`Supabase error: ${error.message}`);
    }

    console.log("[LeadManager] Supabase insert successful:", data);
    return new Response(
      JSON.stringify({ success: true, method: "supabase", data }),
      {
        status: 200,
        headers: { "Content-Type": "application/json" },
      }
    );
  } catch (supabaseError) {
    console.warn(
      "[LeadManager] Supabase insert failed. Trying fallbacks.",
      supabaseError
    );
  }

  // Fallback: Zapier webhook or other API endpoints
  const fallbackEndpoints = [
    import.meta.env?.VITE_ZAPIER_WEBHOOK_URL as string | undefined,
    "/api/lead", // Example Vercel function
  ].filter(Boolean) as string[];

  for (const url of fallbackEndpoints) {
    try {
      console.log(`[LeadManager] Attempting fallback endpoint: ${url}`);
      const res = await fetch(url, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });
      if (!res.ok)
        throw new Error(`Fallback failed with status: ${res.status}`);
      console.log("[LeadManager] Fallback submission successful.");
      return res;
    } catch (e) {
      console.warn(`[LeadManager] Fallback to ${url} failed.`, e);
    }
  }

  throw new Error("[LeadManager] All lead submission methods failed.");
}
