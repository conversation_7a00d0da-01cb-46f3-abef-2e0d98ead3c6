import { Button } from "@/components/ui/button";
import { AlertTriangle, Home, RefreshCw } from "lucide-react";
import { Component, ErrorInfo, ReactNode } from "react";

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error("ErrorBoundary caught an error:", error, errorInfo);
    this.setState({ errorInfo });
  }

  handleReset = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = "/";
  };

  render() {
    const { fallback } = this.props;
    const { hasError, error, errorInfo } = this.state;

    if (!hasError) return this.props.children;

    if (fallback) return fallback;

    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50 p-4">
        <div className="w-full max-w-md rounded-lg bg-white p-6 text-center shadow-md">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            <AlertTriangle className="h-6 w-6 text-red-600" />
          </div>

          <h1 className="text-2xl font-semibold text-gray-900">
            Something went wrong
          </h1>

          <p className="mt-2 text-muted-foreground">
            We've encountered an unexpected error. Please try again.
          </p>

          {process.env.NODE_ENV === "development" && error && (
            <div className="mt-6 rounded-md bg-gray-50 p-4 text-left">
              <p className="mb-2 font-mono text-sm text-red-600">
                {error.toString()}
              </p>
              {errorInfo && (
                <details className="text-xs text-gray-600">
                  <summary className="cursor-pointer">Component Stack</summary>
                  <pre className="mt-2 max-h-40 overflow-auto whitespace-pre-wrap">
                    {errorInfo.componentStack}
                  </pre>
                </details>
              )}
            </div>
          )}

          <div className="mt-6 flex justify-center gap-3">
            <Button onClick={this.handleGoHome} variant="outline">
              <Home className="mr-2 h-4 w-4" />
              Go Home
            </Button>

            <Button onClick={this.handleReset}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }
}

export default ErrorBoundary;
