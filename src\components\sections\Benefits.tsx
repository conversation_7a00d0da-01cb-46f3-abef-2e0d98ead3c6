import { motion } from "framer-motion";
import {
  Award,
  Clock,
  DollarSign,
  Globe,
  Target,
  TrendingUp,
} from "lucide-react";
import React, { memo } from "react";

const Benefits: React.FC = memo(() => {
  const benefits = [
    {
      icon: <Clock className="h-6 w-6 text-primary" />,
      title: "Operational Efficiency",
      metric: "20-30%",
      description:
        "Reduction in tumor board preparation time with minimized errors from data silos.",
      detail: "Streamlined workflows save hours of manual data compilation",
    },
    {
      icon: <Target className="h-6 w-6 text-accent" />,
      title: "Clinical Outcomes",
      metric: "Faster",
      description:
        "Enables faster, informed decisions with improved trial matching and personalized care.",
      detail: "Enhanced precision in treatment planning and patient outcomes",
    },
    {
      icon: <Globe className="h-6 w-6 text-success" />,
      title: "Accessibility",
      metric: "Tier 2/3",
      description:
        "Empowers tier-2/3 hospitals with low-cost interoperability solutions.",
      detail: "Democratizing advanced healthcare technology across India",
    },
    {
      icon: <DollarSign className="h-6 w-6 text-primary" />,
      title: "Economic Impact",
      metric: "2-3x ROI",
      description:
        "Projected return on investment within one year of implementation.",
      detail:
        "Aligns with India's USD 10.7 billion connected health market by 2030",
    },
  ];

  const stats = [
    { label: "ABHA IDs Created", value: "73.98 Cr", growth: "+15%" },
    { label: "Health Facilities", value: "3.63 L", growth: "+22%" },
    { label: "Verified Professionals", value: "5.65 L", growth: "+18%" },
    { label: "Target by 2025", value: "500M+", growth: "Growing" },
  ];

  return (
    <section id="benefits" className="bg-background py-20">
      <div className="container-optimized">
        {/* Section Heading */}
        <motion.div
          className="space-y-4 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <div className="inline-flex items-center gap-2 rounded-full bg-success/10 px-4 py-2 text-sm font-medium text-success">
            <div className="h-2 w-2 animate-pulse rounded-full bg-success"></div>
            Impact & Benefits
          </div>
          <h2 className="lg:text-display-large mb-4 text-heading-1 text-foreground">
            Transforming
            <span className="block text-primary"> Cancer Care Delivery</span>
          </h2>
          <p className="mx-auto max-w-3xl text-xl text-muted-foreground">
            Our interoperability platform delivers measurable improvements in
            efficiency, outcomes, and accessibility across India's healthcare
            ecosystem.
          </p>
        </motion.div>

        {/* Benefits Grid */}
        <div className="my-16 grid gap-8 lg:grid-cols-2">
          {benefits.map((benefit, index) => (
            <motion.div
              key={index}
              className="group flex rounded-2xl border border-border bg-card p-6 transition-all duration-300 hover:shadow-md"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <div className="flex-shrink-0">{benefit.icon}</div>
              <div className="ml-4 flex-1">
                <div className="mb-2 flex items-center justify-between">
                  <h3 className="text-xl font-semibold text-foreground">
                    {benefit.title}
                  </h3>
                  <span className="bg-gradient-primary rounded-full px-3 py-1 text-sm font-bold text-white">
                    {benefit.metric}
                  </span>
                </div>
                <p className="text-body-base mb-1 text-muted-foreground">
                  {benefit.description}
                </p>
                <p className="text-sm italic text-muted-foreground/80">
                  {benefit.detail}
                </p>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Image with Overlay */}
        <div className="relative mb-16">
          <img
            src="/media/tumor-board.jpg"
            alt="AI-powered tumor board meeting"
            className="w-full rounded-3xl shadow-lg"
          />
          <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-primary/20 to-transparent"></div>

          <motion.div
            className="absolute left-6 top-6 rounded-2xl border bg-card/95 p-4 shadow-md backdrop-blur-sm"
            initial={{ scale: 0.8, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <div className="text-sm text-muted-foreground">
              Preparation Time Saved
            </div>
            <div className="text-xl font-bold text-success">20-30%</div>
          </motion.div>

          <motion.div
            className="absolute bottom-6 right-6 rounded-2xl border bg-card/95 p-4 shadow-md backdrop-blur-sm"
            initial={{ scale: 0.8, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <div className="text-sm text-muted-foreground">
              Clinical Accuracy
            </div>
            <div className="text-xl font-bold text-accent">+85%</div>
          </motion.div>
        </div>

        {/* ABDM Stats */}
        <div className="mb-20 text-center">
          <div className="mb-4 inline-flex items-center gap-2 rounded-full bg-accent-light px-4 py-2 text-sm font-medium text-accent">
            🇮🇳 ABDM Ecosystem Growth
          </div>
          <h3 className="mb-4 text-heading-2 text-foreground">
            Aligned with India's Digital Health Mission
          </h3>
          <p className="text-body-base mx-auto mb-8 max-w-2xl text-muted-foreground">
            Leveraging the growing ABDM infrastructure to accelerate healthcare
            digitization.
          </p>

          <div className="grid gap-6 md:grid-cols-4">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                className="rounded-2xl border bg-card p-6 text-center transition-all duration-300 hover:shadow-md"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <div className="mb-1 text-2xl font-bold text-foreground">
                  {stat.value}
                </div>
                <div className="mb-2 text-sm text-muted-foreground">
                  {stat.label}
                </div>
                <div className="inline-flex items-center justify-center gap-1 rounded-full bg-success-light px-2 py-1 text-xs font-medium text-success">
                  <TrendingUp className="h-3 w-3" /> {stat.growth}
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Compliance Section */}
        <div className="rounded-2xl bg-gradient-hero p-12 text-center text-white">
          <div className="mb-6 flex items-center justify-center gap-3">
            <Award className="h-8 w-8" />
            <h3 className="text-2xl font-bold">
              Regulatory Compliance Excellence
            </h3>
          </div>
          <p className="mx-auto mb-8 max-w-3xl text-lg opacity-90">
            Fully compliant with DPDP Act 2023, ABDM Guidelines, FHIR R4
            standards, and international regulations including GDPR, HIPAA, and
            FDA guidelines.
          </p>
          <div className="grid gap-4 text-sm md:grid-cols-3">
            <div className="rounded-lg bg-white/10 p-3">
              <div className="font-semibold">Local Compliance</div>
              <div className="opacity-90">DPDP • CDSCO • ICMR • NDHM</div>
            </div>
            <div className="rounded-lg bg-white/10 p-3">
              <div className="font-semibold">Global Standards</div>
              <div className="opacity-90">GDPR • HIPAA • FDA • EU AI Act</div>
            </div>
            <div className="rounded-lg bg-white/10 p-3">
              <div className="font-semibold">Technical Standards</div>
              <div className="opacity-90">FHIR R4 • HL7 • DICOM • IHE</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
});

Benefits.displayName = "Benefits";

export default Benefits;
