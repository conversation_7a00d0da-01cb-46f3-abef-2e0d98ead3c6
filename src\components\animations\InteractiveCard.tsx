import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import React, { memo, ReactNode, useCallback, useRef } from "react";

interface InteractiveCardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: ReactNode;
  lift?: boolean;
  tilt?: boolean;
  glow?: boolean;
  parallax?: boolean;
}

export const InteractiveCard: React.FC<InteractiveCardProps> = memo(
  ({
    children,
    className,
    lift = true,
    tilt = false,
    glow = false,
    parallax = false,
    onMouseMove,
    onMouseLeave,
    ...props
  }) => {
    const cardRef = useRef<HTMLDivElement>(null);

    const handleMouseMove = useCallback(
      (event: React.MouseEvent<HTMLDivElement>) => {
        if (!tilt || !cardRef.current) {
          onMouseMove?.(event);
          return;
        }

        const card = cardRef.current;
        const rect = card.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        const centerX = rect.width / 2;
        const centerY = rect.height / 2;
        const rotateX = (y - centerY) / 10;
        const rotateY = (centerX - x) / 10;

        card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(0)`;

        onMouseMove?.(event);
      },
      [tilt, onMouseMove]
    );

    const handleMouseLeave = useCallback(
      (event: React.MouseEvent<HTMLDivElement>) => {
        if (!tilt || !cardRef.current) {
          onMouseLeave?.(event);
          return;
        }

        cardRef.current.style.transform =
          "perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(0)";

        onMouseLeave?.(event);
      },
      [tilt, onMouseLeave]
    );

    return (
      <Card
        ref={cardRef}
        className={cn(
          "transition-all duration-300",
          lift && "hover-lift",
          glow && "hover-glow",
          tilt && "transform-gpu",
          className
        )}
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
        {...props}
      >
        {children}
      </Card>
    );
  }
);

InteractiveCard.displayName = "InteractiveCard";
