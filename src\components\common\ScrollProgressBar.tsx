import { motion, useScroll, useSpring } from "framer-motion";
import React from "react";

export const ScrollProgressBar: React.FC = () => {
  const { scrollYProgress } = useScroll();
  const scaleX = useSpring(scrollYProgress, {
    stiffness: 120,
    damping: 25,
    restDelta: 0.001,
  });

  return (
    <motion.div
      aria-hidden
      className="fixed left-0 right-0 top-0 z-50 h-2 origin-left"
      style={{
        background:
          "linear-gradient(90deg,var(--tw-ring-color, #06b6d4), rgba(6,182,212,0.65))",
        transformOrigin: "left",
        scaleX,
        boxShadow: "0 2px 6px rgba(0,0,0,0.08)",
      }}
    />
  );
};

export default ScrollProgressBar;
