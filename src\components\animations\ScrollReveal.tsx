import {
  useScrollAnimation,
  useStaggeredScrollAnimation,
} from "@/hooks/useScrollAnimation";
import { cn } from "@/lib/utils";
import React, { ReactNode, memo } from "react";

type AnimationType =
  | "fade-up"
  | "fade-down"
  | "fade-left"
  | "fade-right"
  | "scale-in"
  | "rotate-in";

interface ScrollRevealProps {
  children: ReactNode;
  animation?: AnimationType;
  delay?: number;
  threshold?: number;
  className?: string;
  triggerOnce?: boolean;
}

export const ScrollReveal: React.FC<ScrollRevealProps> = memo(
  ({
    children,
    animation = "fade-up",
    delay = 0,
    threshold = 0.1,
    className,
    triggerOnce = true,
  }) => {
    const { ref, isVisible } = useScrollAnimation({
      threshold,
      delay,
      triggerOnce,
    });

    return (
      <div
        ref={ref}
        className={cn(
          "scroll-reveal",
          isVisible && `animate-${animation}`,
          className
        )}
      >
        {children}
      </div>
    );
  }
);

ScrollReveal.displayName = "ScrollReveal";

interface StaggeredRevealProps {
  children: ReactNode[];
  delay?: number;
  threshold?: number;
  className?: string;
}

export const StaggeredReveal: React.FC<StaggeredRevealProps> = memo(
  ({ children, delay = 100, threshold = 0.1, className }) => {
    const { ref, visibleItems } = useStaggeredScrollAnimation(children.length, {
      threshold,
      delay,
    });

    return (
      <div ref={ref} className={cn("space-y-4", className)}>
        {children.map((child, index) => (
          <div
            key={index}
            className={cn(
              "scroll-reveal",
              visibleItems[index] && `animate-stagger-${Math.min(index + 1, 5)}`
            )}
          >
            {child}
          </div>
        ))}
      </div>
    );
  }
);

StaggeredReveal.displayName = "StaggeredReveal";
