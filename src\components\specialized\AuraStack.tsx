"use client";

import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import React, { useEffect, useMemo, useRef, useState } from "react";

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

interface AuraStackProps {
  className?: string;
  onStageChange?: (stage: number) => void;
  enableAnimations?: boolean;
}

const STAGES: Array<{ title: string; copy: string; id: string }> = [
  {
    id: "data",
    title: "DATA",
    copy: "Aggregate EMR, LIS, and PACS data into a unified pipeline.",
  },
  {
    id: "context",
    title: "CONTEXT",
    copy: "Normalize to FHIR, map entities, and establish provenance.",
  },
  {
    id: "insight",
    title: "INSIGHT",
    copy: "Summarize longitudinal signals into actionable insights.",
  },
  {
    id: "coaching",
    title: "COACHING",
    copy: "Surface quick wins and cohort-based guidance for clinicians.",
  },
  {
    id: "orchestration",
    title: "ORCHESTRATION",
    copy: "Automate follow-ups and task routing across the care team.",
  },
  {
    id: "aura-guardian",
    title: "AURA GUARDIAN",
    copy: "Always-on safety checks and guidance—human-in-the-loop by design.",
  },
];

const AuraStack: React.FC<AuraStackProps> = ({
  className = "",
  onStageChange,
  enableAnimations = true,
}) => {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const stackRef = useRef<HTMLDivElement | null>(null);
  const platesRef = useRef<Array<HTMLDivElement | null>>([]);

  const [currentStage, setCurrentStage] = useState<number>(-1);
  const currentStageRef = useRef<number>(-1);
  const [isIntersecting, setIsIntersecting] = useState(false);

  const visualLayers = useMemo(
    () => [
      { id: "base", title: "Foundation", path: "/aura-stack/00-base.png" },
      { id: "data", title: "DATA", path: "/aura-stack/01-data.png" },
      { id: "context", title: "CONTEXT", path: "/aura-stack/02-context.png" },
      { id: "insight", title: "INSIGHT", path: "/aura-stack/03-insight.png" },
      {
        id: "coaching",
        title: "COACHING",
        path: "/aura-stack/04-coaching.png",
      },
      {
        id: "orchestration",
        title: "ORCHESTRATION",
        path: "/aura-stack/05-orchestration.png",
      },
      { id: "aura", title: "AURA GUARDIAN", path: "/aura-stack/06-aura.png" },
      { id: "cap", title: "Entheory", path: "/aura-stack/07-cap.png" },
    ],
    []
  );

  const CALIB: Record<string, { scale: number; y: number }> = useMemo(
    () => ({
      base: { scale: 1, y: 0 },
      data: { scale: 1, y: 0 },
      context: { scale: 1, y: 0 },
      insight: { scale: 1, y: 0 },
      coaching: { scale: 1, y: 0 },
      orchestration: { scale: 1, y: 0 },
      aura: { scale: 1, y: 0 },
      cap: { scale: 1, y: 0 },
    }),
    []
  );

  const handleStageChange = (stage: number) => {
    currentStageRef.current = stage;
    setCurrentStage(stage);
    onStageChange?.(stage);
  };

  useEffect(() => {
    const node = containerRef.current;
    if (!node) return;
    const observer = new IntersectionObserver(
      ([entry]) => setIsIntersecting(entry.isIntersecting),
      { threshold: 0.1, rootMargin: "50px" }
    );
    observer.observe(node);
    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    if (!stackRef.current || !enableAnimations) return;

    const plates = platesRef.current.filter(Boolean);
    if (plates.length === 0) return;

    ScrollTrigger.getAll().forEach((st) => st.kill());

    const plateSpacing = 16;
    const timeline = gsap.timeline({
      scrollTrigger: {
        trigger: stackRef.current,
        start: "top bottom",
        end: () => `+=${plates.length * 100}px`,
        scrub: 0.8,
        pin: true,
        pinSpacing: false,
        invalidateOnRefresh: true,
        onUpdate: (self) => {
          const progress = Math.max(0, Math.min(1, self.progress || 0));
          const stageIndex = Math.min(
            Math.floor(progress * STAGES.length),
            STAGES.length - 1
          );
          if (stageIndex !== currentStageRef.current) {
            currentStageRef.current = stageIndex;
            setCurrentStage(stageIndex);
            onStageChange?.(stageIndex);
          }
        },
      },
    });

    plates.forEach((plate, index) => {
      if (!plate) return;
      const targetY = index === 0 ? plateSpacing : 0 - index * plateSpacing;
      timeline.fromTo(
        plate,
        { y: 400, opacity: 0, scale: 0.95, rotateX: -20, z: index * 20 },
        {
          y: targetY,
          opacity: 1,
          scale: 1,
          rotateX: -15,
          duration: 0.5,
          ease: "power2.inOut",
        },
        index * 0.1
      );
    });

    return () => ScrollTrigger.getAll().forEach((st) => st.kill());
  }, [enableAnimations, onStageChange]);

  return (
    <div
      id="aura-stack"
      ref={containerRef}
      className={`relative overflow-hidden ${className}`}
      style={{ minHeight: "100vh" }}
      aria-label="Aura Stack visualization"
    >
      <div className="absolute inset-0 -z-10" aria-hidden="true">
        <picture>
          <source
            media="(max-width: 768px)"
            srcSet="/media/aura/aura-bg-mobile.png"
            type="image/png"
          />
          <img
            src="/media/aura/aura-bg-desktop.png"
            alt=""
            className="h-full w-full object-cover"
            style={{
              backgroundSize: "cover",
              backgroundPosition: "center",
              backgroundRepeat: "no-repeat",
            }}
            loading="lazy"
            decoding="async"
          />
        </picture>
      </div>

      {enableAnimations && (
        <>
          <style>
            {`@keyframes gradientShift{0%,100%{transform:translate3d(0,0,0) scale(1);}50%{transform:translate3d(-1%,0.5%,0) scale(1.01);}} @keyframes driftSlow{0%,100%{transform:translate3d(0,0,0) rotate(0deg);}50%{transform:translate3d(2%,-1%,0) rotate(4deg);}}`}
          </style>
          <div
            className="pointer-events-none absolute inset-0 overflow-hidden"
            aria-hidden
          >
            <div
              className="absolute -inset-16 opacity-70"
              style={{
                background:
                  "radial-gradient(800px 600px at 20% 20%, rgba(255,183,135,0.3), transparent 60%), radial-gradient(700px 500px at 80% 80%, rgba(255,176,193,0.25), transparent 60%)",
                filter: "blur(1px)",
                animation: "gradientShift 20s ease-in-out infinite",
              }}
            />
            <div
              className="absolute -left-20 -top-32 h-[40vw] w-[40vw] rounded-full blur-2xl"
              style={{
                background:
                  "radial-gradient(circle at 40% 40%, rgba(255,176,193,0.4), transparent 60%)",
                opacity: 0.5,
                animation: "driftSlow 30s ease-in-out infinite",
              }}
            />
            <div
              className="absolute -bottom-32 -right-20 h-[35vw] w-[35vw] rounded-full blur-2xl"
              style={{
                background:
                  "radial-gradient(circle at 60% 40%, rgba(255,236,153,0.35), transparent 60%)",
                opacity: 0.45,
                animation: "driftSlow 25s ease-in-out infinite reverse",
              }}
            />
            <div
              className="absolute inset-0"
              style={{
                background:
                  "radial-gradient(100% 70% at 50% 50%, transparent 50%, rgba(0,0,0,0.04) 100%)",
              }}
            />
          </div>
        </>
      )}

      <div className="relative z-10 flex min-h-screen items-center py-6 sm:py-8 lg:py-0">
        <div className="container mx-auto w-full px-3 sm:px-4 lg:px-6 xl:px-8">
          <div className="grid items-center gap-4 lg:grid-cols-2 lg:gap-12">
            <div className="relative z-10 order-2 text-center lg:order-1 lg:text-left">
              <h2 className="mb-3 text-2xl font-light leading-tight text-stone-900 sm:text-3xl md:text-4xl lg:mb-4 lg:text-5xl xl:text-6xl">
                The Aura
                <br />
                <em className="font-light italic">Stack.</em>
              </h2>
              <p className="mx-auto max-w-md text-sm font-light leading-relaxed text-stone-700/90 sm:text-base lg:mx-0 lg:text-base xl:text-lg">
                Orchestrating oncology care with AI. From scattered signals to
                steady care—one intelligent system.
              </p>
            </div>

            <div className="relative flex min-h-screen items-center justify-center">
              <div
                ref={stackRef}
                className="sm:scale-85 relative scale-75 lg:scale-95 xl:scale-100"
                style={{
                  transformStyle: "preserve-3d",
                  perspective: "1200px",
                  perspectiveOrigin: "50% 50%",
                }}
                role="img"
                aria-label="Interactive 3D stack visualization"
              >
                {visualLayers.map((layer, index) => (
                  <div
                    key={layer.id}
                    ref={(el) => (platesRef.current[index] = el)}
                    className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"
                    style={{
                      width: "320px",
                      height: "320px",
                      transformStyle: "preserve-3d",
                      willChange: "transform, opacity",
                    }}
                  >
                    <img
                      src={layer.path}
                      alt={layer.title}
                      className="h-full w-full origin-bottom object-contain"
                      style={{
                        transform: `scale(${CALIB[layer.id]?.scale ?? 1}) translateY(${CALIB[layer.id]?.y ?? 0}px)`,
                        filter: `drop-shadow(0 ${8 + index * 1}px ${16 + index * 2}px rgba(0,0,0,${0.08 + index * 0.015}))`,
                      }}
                      loading="lazy"
                      decoding="async"
                    />
                  </div>
                ))}
              </div>

              <div className="absolute right-0 top-1/2 z-20 mr-6 hidden w-72 -translate-y-1/2 lg:block xl:mr-8 xl:w-80">
                {currentStage >= 0 && currentStage < STAGES.length && (
                  <div
                    className="rounded-lg bg-white/90 p-3 shadow-lg backdrop-blur-sm transition-all duration-300"
                    role="region"
                    aria-live="polite"
                    aria-label={`Current stage: ${STAGES[currentStage].title}`}
                    style={{ maxWidth: "320px" }}
                  >
                    <div className="relative flex items-center">
                      <div className="absolute -left-12 flex items-center xl:-left-16">
                        <div className="relative h-8 w-10 xl:h-10 xl:w-12">
                          <div className="absolute left-0 top-1/2 h-px w-6 bg-gray-900 xl:w-8" />
                          <div className="absolute left-6 top-1/2 h-1.5 w-1.5 -translate-y-1/2 rounded-full bg-gray-900 xl:left-8" />
                        </div>
                      </div>
                      <div className="pl-2">
                        <h3 className="mb-2 text-lg font-light text-stone-900 xl:mb-3 xl:text-xl">
                          {STAGES[currentStage].title}
                        </h3>
                        <p className="max-w-sm text-sm font-light leading-relaxed text-stone-700/90 xl:max-w-md xl:text-base">
                          {STAGES[currentStage].copy}
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className="absolute -bottom-12 left-1/2 w-full max-w-sm -translate-x-1/2 px-4 lg:hidden">
                {currentStage >= 0 && currentStage < STAGES.length && (
                  <div
                    className="rounded-lg bg-white/95 p-3 text-center shadow-lg backdrop-blur-sm transition-all duration-300"
                    role="region"
                    aria-live="polite"
                    aria-label={`Current stage: ${STAGES[currentStage].title}`}
                  >
                    <h3 className="mb-1 text-base font-medium text-stone-900">
                      {STAGES[currentStage].title}
                    </h3>
                    <p className="text-xs leading-relaxed text-stone-700/90">
                      {STAGES[currentStage].copy}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

AuraStack.displayName = "AuraStack";

export default AuraStack;
export type { AuraStackProps };
