"use client";

import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import React, { memo } from "react";

export type LoadingSpinnerProps = {
  size?: "sm" | "md" | "lg";
  className?: string;
};

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = memo(
  ({ size = "md", className = "" }) => {
    const sizeMap = { sm: 20, md: 32, lg: 48 };
    const px = sizeMap[size];
    return (
      <motion.svg
        width={px}
        height={px}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        animate={{ rotate: 360 }}
        transition={{ duration: 1.2, repeat: Infinity, ease: "linear" }}
        className={cn("text-primary", className)}
        role="status"
        aria-live="polite"
      >
        <defs>
          <linearGradient
            id="spinner-gradient"
            x1="0%"
            y1="0%"
            x2="100%"
            y2="0%"
          >
            <stop offset="0%" stopColor="hsl(var(--primary))" />
            <stop offset="100%" stopColor="hsl(var(--primary-foreground))" />
          </linearGradient>
        </defs>
        <circle
          cx="12"
          cy="12"
          r="10"
          stroke="url(#spinner-gradient)"
          strokeWidth="2.5"
          strokeLinecap="round"
          strokeDasharray="70"
          strokeDashoffset="25"
          opacity="0.9"
        />
      </motion.svg>
    );
  }
);

LoadingSpinner.displayName = "LoadingSpinner";

export const PageLoadingState: React.FC<{
  logoSrc: string;
  subtitle?: string;
}> = memo(({ logoSrc, subtitle }) => (
  <motion.div
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    exit={{ opacity: 0 }}
    transition={{ duration: 0.5 }}
    className="fixed inset-0 z-[100] flex min-h-screen flex-col items-center justify-center bg-background"
  >
    <motion.img
      src={logoSrc}
      alt="logo"
      className="h-28 w-auto drop-shadow-xl"
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{
        opacity: 1,
        scale: [1, 1.05, 1],
        filter: [
          "drop-shadow(0 0 0px hsl(var(--primary)))",
          "drop-shadow(0 0 12px hsl(var(--primary)))",
          "drop-shadow(0 0 0px hsl(var(--primary)))",
        ],
      }}
      transition={{
        duration: 2,
        repeat: Infinity,
        repeatType: "mirror",
        ease: "easeInOut",
      }}
    />

    {subtitle && (
      <motion.p
        className="mt-5 text-center text-lg font-medium text-muted-foreground"
        initial={{ opacity: 0, y: 8 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3, duration: 0.6 }}
      >
        {subtitle}
      </motion.p>
    )}

    <motion.div
      className="mt-8"
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ delay: 0.5, duration: 0.5 }}
    >
      <LoadingSpinner size="lg" />
    </motion.div>

    <motion.div
      className="mt-4 text-sm font-medium text-muted-foreground"
      initial={{ opacity: 0 }}
      animate={{ opacity: [0.4, 1, 0.4] }}
      transition={{ duration: 1.5, repeat: Infinity }}
    >
      Loading…
    </motion.div>
  </motion.div>
));

PageLoadingState.displayName = "PageLoadingState";

export default PageLoadingState;
