// This file is automatically generated. Do not edit it directly.
import { createClient } from "@supabase/supabase-js";
import type { Database } from "./types";

// Prefer env from Vite; fall back to generated defaults to keep dev working.
const SUPABASE_URL =
  (import.meta as any)?.env?.VITE_SUPABASE_URL ||
  "https://ipaxnixvzawlwgjwcpup.supabase.co";
const SUPABASE_PUBLISHABLE_KEY =
  (import.meta as any)?.env?.VITE_SUPABASE_PUBLISHABLE_KEY ||
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlwYXhuaXh2emF3bHdnandjcHVwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTU2MzQ4NDAsImV4cCI6MjA3MTIxMDg0MH0.QsrnaNmbGAH9e6Qlg-54B12E2_AxWQeOXCTGmhgnpQc";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(
  SUPABASE_URL,
  SUPABASE_PUBLISHABLE_KEY,
  {
    auth: {
      storage: localStorage,
      persistSession: true,
      autoRefreshToken: true,
    },
  }
);
