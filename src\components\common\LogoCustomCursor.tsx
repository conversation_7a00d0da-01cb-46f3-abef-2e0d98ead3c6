import { motion, useMotionValue, useSpring } from "framer-motion";
import React, { useCallback, useEffect, useState } from "react";

const CustomCursor: React.FC = () => {
  const [isPointer, setIsPointer] = useState(false);
  const cursorX = useMotionValue(-100);
  const cursorY = useMotionValue(-100);

  const springConfig = { damping: 25, stiffness: 500, mass: 0.1 };
  const cursorXSpring = useSpring(cursorX, springConfig);
  const cursorYSpring = useSpring(cursorY, springConfig);

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      cursorX.set(e.clientX);
      cursorY.set(e.clientY);

      const target = e.target as HTMLElement;
      const isPointerElement = !!target.closest(
        "a, button, [data-cursor='pointer']"
      );
      setIsPointer(isPointerElement);
    },
    [cursorX, cursorY, isPointer]
  );

  useEffect(() => {
    document.addEventListener("mousemove", handleMouseMove);
    document.body.style.cursor = "none";

    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.body.style.cursor = "auto";
    };
  }, [handleMouseMove]);

  return (
    <motion.div
      className="animate-aurora-pulse pointer-events-none fixed z-[9999] rounded-2xl"
      style={{
        left: cursorXSpring,
        top: cursorYSpring,
        x: "-50%",
        y: "-50%",
        backgroundImage: "url(/favicon.ico)",
        backgroundSize: "contain",
        backgroundRepeat: "no-repeat",
        backgroundPosition: "center",
      }}
      animate={{
        width: isPointer ? 48 : 32,
        height: isPointer ? 48 : 32,
        rotate: isPointer ? 15 : 0,
        boxShadow: isPointer
          ? "0 0 25px 8px hsl(var(--primary) / 0.4)"
          : "0 0 0px 0px hsl(var(--primary) / 0)",
      }}
      transition={{ type: "spring", stiffness: 600, damping: 30 }}
    />
  );
};

export default CustomCursor;
