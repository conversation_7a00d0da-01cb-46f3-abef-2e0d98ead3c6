import GSAPManager from "@/lib/gsapManager";
import { cn } from "@/lib/utils";
import React, { memo, useEffect, useRef } from "react";

interface Props {
  from: string;
  to: string;
  height?: string;
  gradient?: string;
}

export const InterSectionTransition: React.FC<Props> = memo(
  ({ from, to, height, gradient }) => {
    const ref = useRef<HTMLDivElement | null>(null);

    useEffect(() => {
      if (!ref.current) return;

      const fromEl = document.querySelector(from) as HTMLElement | null;
      const toEl = document.querySelector(to) as HTMLElement | null;
      if (!fromEl || !toEl) {
        ref.current.style.height = "0px";
        return;
      }

      const contextId = `transition-${from}-${to}`;
      const ctx = GSAPManager.createContext(contextId, ref.current);

      ctx.add(() => {
        const pinnedElements = GSAPManager.ScrollTrigger.getAll().filter(
          (st: any) =>
            st.vars.pin &&
            (st.vars.trigger === fromEl || st.vars.trigger === toEl)
        );

        if (pinnedElements.length > 0) return;

        const tl = GSAPManager.gsap.timeline({
          scrollTrigger: {
            trigger: ref.current,
            start: "top bottom",
            end: "bottom top",
            scrub: 1.2,
            refreshPriority: 1,
          },
        });

        tl.to(
          fromEl,
          { opacity: 0, scale: 0.985, duration: 0.6, ease: "power1.inOut" },
          0
        ).fromTo(
          toEl,
          { opacity: 0, scale: 0.985 },
          { opacity: 1, scale: 1, duration: 0.8, ease: "power1.out" },
          "-=0.45"
        );
      });

      return () => GSAPManager.cleanContext(contextId);
    }, [from, to, height, gradient]);

    return (
      <div ref={ref} aria-hidden className={cn("relative w-full", height)}>
        {gradient && (
          <div
            className="pointer-events-none absolute inset-0"
            style={{ background: gradient }}
          />
        )}
      </div>
    );
  }
);

InterSectionTransition.displayName = "InterSectionTransition";
export default InterSectionTransition;
