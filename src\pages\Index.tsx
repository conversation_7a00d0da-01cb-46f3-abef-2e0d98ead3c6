import {
  layoutTransition,
  layoutVariants,
  sectionVariants,
} from "@/lib/animations";
import { motion } from "framer-motion";
import React, {
  lazy,
  memo,
  Suspense,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";

import InterSectionTransition from "@/components/common/InterSectionTransition";
import PageLoadingState from "@/components/common/LoadingStates";
import PageTransitionController from "@/components/common/PageTransitionController";
import ScrollProgressBar from "@/components/common/ScrollProgressBar";
import Footer from "@/components/layout/Footer";
import Header from "@/components/layout/Header";
import GSAPManager from "@/lib/gsapManager";

const Hero = lazy(() => import("@/components/sections/Hero"));
const AuraStack = lazy(() => import("@/components/specialized/AuraStack"));
const AuraBranches = lazy(
  () => import("@/components/specialized/AuraBranches")
);
const ProblemSolution = lazy(
  () => import("@/components/sections/ProblemSolution")
);
const Features = lazy(() => import("@/components/sections/Features"));
const Benefits = lazy(() => import("@/components/sections/Benefits"));
const ValuesSection = lazy(() => import("@/components/sections/ValuesSection"));
const SystemStandards = lazy(
  () => import("@/components/sections/SystemStandards")
);
const ScrollytellingSection = lazy(
  () => import("@/components/sections/ScrollytellingSection")
);
const FinalCTA = lazy(() => import("@/components/sections/FinalCTA"));
const About = lazy(() => import("@/components/sections/About"));
const EntheoryHeroShader = lazy(
  () => import("@/components/sections/EntheoryHeroShader")
);

const LazyFallback = memo(() => <div className="min-h-screen" />);
LazyFallback.displayName = "LazyFallback";

const SECTIONS = [
  { id: "hero", component: Hero, animated: true, skipTransition: true },
  {
    id: "aura-stack",
    component: AuraStack,
    animated: true,
    transition: {
      from: "#hero",
      gradient:
        "linear-gradient(180deg, hsl(220 85% 58% / 0.05) 0%, hsl(330 80% 95% / 0.1) 100%)",
    },
  },
  {
    id: "aura-branches",
    component: AuraBranches,
    animated: true,
    transition: {
      from: "#aura-stack",
      gradient:
        "linear-gradient(180deg, hsl(50 100% 90% / 0.1) 0%, hsl(180 90% 45% / 0.05) 100%)",
    },
  },
  {
    id: "about",
    component: About,
    animated: true,
    transition: {
      from: "#aura-branches",
      gradient:
        "linear-gradient(180deg, hsl(180 90% 45% / 0.05) 0%, hsl(220 85% 95% / 0.1) 100%)",
    },
  },
  {
    id: "problem-solution",
    component: ProblemSolution,
    animated: true,
    transition: {
      from: "#about",
      gradient:
        "linear-gradient(180deg, hsl(220 85% 95% / 0.1) 0%, hsl(150 70% 95% / 0.15) 100%)",
    },
  },
  {
    id: "entheory-hero-shader",
    component: EntheoryHeroShader,
    animated: true,
    transition: {
      from: "#problem-solution",
      gradient:
        "linear-gradient(180deg, hsl(150 70% 95% / 0.15) 0%, hsl(260 60% 50% / 0.05) 100%)",
    },
  },
  {
    id: "features",
    component: Features,
    animated: true,
    transition: {
      from: "#entheory-hero-shader",
      gradient:
        "linear-gradient(180deg, hsl(260 60% 50% / 0.05) 0%, hsl(45 90% 95% / 0.1) 100%)",
    },
  },
  {
    id: "benefits",
    component: Benefits,
    animated: true,
    transition: {
      from: "#features",
      gradient:
        "linear-gradient(180deg, hsl(45 90% 95% / 0.1) 0%, hsl(140 65% 95% / 0.15) 100%)",
    },
  },
  {
    id: "values",
    component: ValuesSection,
    animated: true,
    transition: {
      from: "#benefits",
      gradient:
        "linear-gradient(180deg, hsl(140 65% 95% / 0.15) 0%, hsl(210 90% 95% / 0.1) 100%)",
    },
  },
  {
    id: "systemstandards",
    component: SystemStandards,
    animated: true,
    transition: {
      from: "#values",
      gradient:
        "linear-gradient(180deg, hsl(210 90% 95% / 0.1) 0%, hsl(320 60% 95% / 0.1) 100%)",
    },
  },
  {
    id: "scrollytelling",
    component: ScrollytellingSection,
    animated: true,
    transition: {
      from: "#section4",
      gradient:
        "linear-gradient(180deg, hsl(320 60% 95% / 0.1) 0%, hsl(220 20% 98% / 0.1) 100%)",
    },
  },
  {
    id: "final-cta",
    component: FinalCTA,
    animated: true,
    transition: {
      from: "#scrollytelling",
      gradient:
        "linear-gradient(180deg, hsl(220 20% 98% / 0.1) 0%, hsl(0 0% 100% / 0.1) 100%)",
    },
  },
];
const Index: React.FC = memo(() => {
  const [isLoading, setIsLoading] = useState(true);

  const handleLoadingComplete = useCallback(() => {
    setIsLoading(false);
    requestAnimationFrame(() => {
      GSAPManager.refresh();
    });
  }, []);

  useEffect(() => {
    const timer = setTimeout(handleLoadingComplete, 1500);
    return () => clearTimeout(timer);
  }, [handleLoadingComplete]);

  const renderedSections = useMemo(
    () =>
      SECTIONS.map(
        ({
          id,
          component: Component,
          animated,
          transition,
          skipTransition,
        }) => (
          <React.Fragment key={id}>
            {transition && !skipTransition && (
              <InterSectionTransition
                from={transition.from}
                to={`#${id}`}
                gradient={transition.gradient}
                height="h-[5vh]"
              />
            )}

            <motion.section
              id={id}
              {...(animated && { "data-animated": true })}
              variants={sectionVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, amount: 0.2 }}
              className="relative"
            >
              <Suspense fallback={<LazyFallback />}>
                <Component />
              </Suspense>
            </motion.section>
          </React.Fragment>
        )
      ),
    []
  );

  return (
    <>
      {isLoading && (
        <PageLoadingState
          logoSrc="/logo.png"
          subtitle="Oncology, unified in 60 seconds"
        />
      )}

      {!isLoading && (
        <PageTransitionController>
          <ScrollProgressBar />

          <motion.header
            variants={layoutVariants}
            initial="initial"
            animate="animate"
            exit="exit"
            transition={layoutTransition}
          >
            <Header />
          </motion.header>

          <motion.main
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="relative"
          >
            {renderedSections}
          </motion.main>

          <motion.footer
            variants={layoutVariants}
            initial="initial"
            animate="animate"
            exit="exit"
            transition={layoutTransition}
          >
            <Footer />
          </motion.footer>
        </PageTransitionController>
      )}
    </>
  );
});

Index.displayName = "Index";

export default Index;
