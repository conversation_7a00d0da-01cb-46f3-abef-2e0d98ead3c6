import { cn } from "@/lib/utils";
import React, { memo, useMemo } from "react";

interface OrganicBackgroundProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
  variant?: "float" | "pulse" | "breathe" | "morph";
  color?: "purple" | "orange" | "blue" | "green";
  size?: "sm" | "md" | "lg" | "xl";
  position?: string;
}

const sizeClasses = {
  sm: "w-48 h-48",
  md: "w-72 h-72",
  lg: "w-96 h-96",
  xl: "w-[28rem] h-[28rem]",
} as const;

const colorClasses = {
  purple: "bg-gradient-purple",
  orange: "bg-gradient-orange",
  blue: "bg-gradient-blue",
  green: "bg-gradient-green",
} as const;

const animationClasses = {
  float: "animate-organic-float",
  pulse: "animate-organic-pulse",
  breathe: "animate-organic-breathe",
  morph: "animate-liquid-morph",
} as const;

export const OrganicBackground: React.FC<OrganicBackgroundProps> = memo(
  ({
    className,
    variant = "float",
    color = "purple",
    size = "md",
    position = "top-1/4 left-1/4",
    style,
    ...props
  }) => {
    return (
      <div
        className={cn(
          "pointer-events-none absolute opacity-15 blur-[40px]",
          "border-radius: 50% 40% 60% 30%",
          sizeClasses[size],
          colorClasses[color],
          animationClasses[variant],
          position,
          className
        )}
        style={style}
        {...props}
      />
    );
  }
);

OrganicBackground.displayName = "OrganicBackground";

interface OrganicBackgroundGridProps {
  className?: string;
  count?: number;
}

export const OrganicBackgroundGrid: React.FC<OrganicBackgroundGridProps> = memo(
  ({ className, count = 4 }) => {
    const backgrounds = useMemo(
      () =>
        Array.from({ length: count }, (_, i) => ({
          id: i,
          variant: (["float", "pulse", "breathe"] as const)[i % 3],
          color: (["purple", "orange", "blue", "green"] as const)[i % 4],
          size: (["md", "lg", "xl"] as const)[i % 3],
          position: [
            "top-1/4 left-1/4",
            "top-3/4 right-1/4",
            "bottom-1/4 left-1/3",
            "top-1/2 right-1/3",
          ][i % 4],
          delay: `${i * 2}s`,
        })),
      [count]
    );

    return (
      <div className={cn("absolute inset-0 overflow-hidden", className)}>
        {backgrounds.map((bg) => (
          <OrganicBackground
            key={bg.id}
            variant={bg.variant}
            color={bg.color}
            size={bg.size}
            position={bg.position}
            style={{ animationDelay: bg.delay }}
          />
        ))}
      </div>
    );
  }
);

OrganicBackgroundGrid.displayName = "OrganicBackgroundGrid";
