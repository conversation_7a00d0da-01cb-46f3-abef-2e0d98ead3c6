import GSAPManager from "@/lib/gsapManager";
import React, { useEffect, useRef } from "react";

interface ScrollControllerProps {
  children: React.ReactNode;
}

export const ScrollControllerContext = React.createContext<{
  timeline: gsap.core.Timeline | null;
}>({
  timeline: null,
});

const ScrollController: React.FC<ScrollControllerProps> = ({ children }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<gsap.core.Timeline | null>(null);

  useEffect(() => {
    if (!containerRef.current) return;
    GSAPManager.ScrollTrigger.getAll().forEach((st) => {
      if (
        st.vars.trigger &&
        (st.vars.trigger as Element).closest("[data-scroll-container]")
      ) {
        st.kill();
      }
    });

    const ctx = GSAPManager.createContext(
      "scroll-container",
      containerRef.current
    );

    const tl = ctx.add(() => {
      return GSAPManager.gsap.timeline({
        scrollTrigger: {
          trigger: containerRef.current,
          start: "top top",
          end: "+=400%",
          scrub: 1,
          pin: false,
          invalidateOnRefresh: true,
          anticipatePin: 1,
        },
      });
    });
    timelineRef.current = tl;
    return () => {
      GSAPManager.cleanContext("scroll-container");
      GSAPManager.ScrollTrigger.getAll().forEach((st) => {
        if (
          st.vars.trigger &&
          (st.vars.trigger as Element).closest("[data-scroll-container]")
        ) {
          st.kill();
        }
      });
    };
  }, []);

  return (
    <ScrollControllerContext.Provider
      value={{
        timeline: timelineRef.current,
      }}
    >
      <div ref={containerRef} data-scroll-container className="relative">
        {children}
      </div>
    </ScrollControllerContext.Provider>
  );
};

export default ScrollController;

export const useScrollController = () => {
  const context = React.useContext(ScrollControllerContext);
  if (!context) {
    throw new Error(
      "useScrollController must be used within a ScrollController provider."
    );
  }
  return context;
};
