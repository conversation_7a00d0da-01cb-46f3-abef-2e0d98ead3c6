import GSAPManager from "@/lib/gsapManager";
import { motion } from "framer-motion";
import React, { useLayoutEffect, useRef } from "react";

interface Props {
  children: React.ReactNode;
}

const PageTransitionController: React.FC<Props> = ({ children }) => {
  const rootRef = useRef<HTMLDivElement>(null);

  useLayoutEffect(() => {
    if (!rootRef.current) return;

    const ctx = GSAPManager.createContext("page-controller", rootRef.current);

    ctx.add(() => {
      const heroTimeline = GSAPManager.gsap.timeline({
        defaults: { ease: "power2.out", duration: 0.7 },
      });

      heroTimeline
        .from("[data-animate-header]", { y: -24, opacity: 0, duration: 0.6 })
        .from("[data-animate-hero-title]", { y: 28, opacity: 0 }, "-=0.3")
        .from("[data-animate-hero-text]", { y: 20, opacity: 0 }, "-=0.4")
        .from("[data-animate-hero-cta]", { y: 16, opacity: 0 }, "-=0.4");

      const sections = GSAPManager.gsap.utils
        .toArray<HTMLElement>("[data-animated]")
        .filter((section: any) => section.id !== "aura-stack");

      sections.forEach((section: any) => {
        GSAPManager.gsap.from(section, {
          y: 50,
          opacity: 0,
          duration: 1.0,
          ease: "power3.out",
          scrollTrigger: {
            trigger: section,
            start: "top 85%",
            toggleActions: "play none none none",
            once: true,
            refreshPriority: 2,
          },
        });
      });
    });

    return () => GSAPManager.cleanContext("page-controller");
  }, []);

  return (
    <motion.div
      ref={rootRef}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5, ease: "easeInOut" }}
    >
      {children}
    </motion.div>
  );
};

export default PageTransitionController;
