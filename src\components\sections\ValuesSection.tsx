import { motion, useInView, useScroll, useTransform } from "framer-motion";
import React, { memo, useMemo } from "react";

interface AnimatedCounterProps {
  value: number;
  suffix?: string;
  prefix?: string;
  delay?: number;
}

const AnimatedCounter: React.FC<AnimatedCounterProps> = memo(
  ({ value, suffix = "", prefix = "", delay = 0 }) => {
    const [count, setCount] = React.useState(0);
    const ref = React.useRef(null);
    const isInView = useInView(ref, { once: true, amount: 0.5 });

    React.useEffect(() => {
      if (!isInView) return;

      const duration = 2000;
      const steps = 60;
      const stepValue = value / steps;
      let current = 0;

      const timer = setTimeout(() => {
        const interval = setInterval(() => {
          current += stepValue;
          if (current >= value) {
            setCount(value);
            clearInterval(interval);
          } else {
            setCount(Math.floor(current));
          }
        }, duration / steps);

        return () => clearInterval(interval);
      }, delay);

      return () => clearTimeout(timer);
    }, [isInView, value, delay]);

    return (
      <span ref={ref}>
        {prefix}
        {count.toLocaleString()}
        {suffix}
      </span>
    );
  }
);

AnimatedCounter.displayName = "AnimatedCounter";

const ValuesSection = memo(() => {
  const containerRef = React.useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"],
  });

  const y1 = useTransform(scrollYProgress, [0, 1], [100, -100]);
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]);

  const metrics = useMemo(
    () => [
      {
        value: 87,
        suffix: "%",
        label: "Reduced TAT",
        sublabel: "Turnaround time for board prep",
      },
      {
        value: 4.8,
        suffix: "x",
        label: "Faster Decisions",
        sublabel: "Clinical decision velocity",
      },
      {
        value: 92,
        suffix: "%",
        label: "Accuracy Rate",
        sublabel: "AI-assisted recommendations",
      },
      {
        value: 3200,
        suffix: "+",
        label: "Patients Served",
        sublabel: "Across partner institutions",
      },
    ],
    []
  );

  const testimonials = useMemo(
    () => [
      {
        quote:
          "Aura has transformed how we prepare for tumor boards. What used to take hours now takes minutes.",
        author: "Dr. Sarah Chen",
        role: "Chief Oncologist, Apollo Hospitals",
        avatar: "SC",
      },
      {
        quote:
          "The AI-powered insights have significantly improved our treatment planning accuracy.",
        author: "Dr. Raj Patel",
        role: "Medical Director, Max Healthcare",
        avatar: "RP",
      },
      {
        quote:
          "Finally, a system that understands the complexity of oncology workflows.",
        author: "Dr. Priya Sharma",
        role: "Head of Oncology, Fortis",
        avatar: "PS",
      },
    ],
    []
  );

  return (
    <section
      ref={containerRef}
      className="relative min-h-screen overflow-hidden bg-gradient-to-br from-slate-50 via-white to-cyan-50"
    >
      <motion.div className="absolute inset-0 opacity-30" style={{ y: y1 }}>
        <div className="absolute left-20 top-20 h-96 w-96 rounded-full bg-gradient-to-br from-cyan-200 to-blue-200 blur-3xl filter" />
        <div className="absolute bottom-20 right-20 h-96 w-96 rounded-full bg-gradient-to-br from-purple-200 to-pink-200 blur-3xl filter" />
      </motion.div>

      <motion.div
        className="container-optimized relative z-10 py-16 lg:py-20"
        style={{ opacity }}
      >
        <motion.div
          className="space-y-4 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <div className="inline-flex items-center gap-2 rounded-full bg-accent/10 px-4 py-2 text-sm font-medium text-accent">
            <div className="h-2 w-2 animate-pulse rounded-full bg-accent"></div>
            Value Provided
          </div>
          <h2 className="lg:text-display-large mb-4 text-heading-1 text-foreground">
            Impact that
            <span className="block text-primary">transforms care</span>
          </h2>
          <p className="mx-auto max-w-3xl text-xl text-muted-foreground">
            Measurable improvements across the entire oncology care continuum.
          </p>
        </motion.div>

        <div className="my-16 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
          {metrics.map((metric, index) => (
            <motion.div
              key={index}
              className="rounded-xl bg-white p-6 shadow-sm transition-all duration-200 hover:shadow-md"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              whileHover={{ y: -2 }}
            >
              <div className="mb-2 text-3xl font-bold text-primary">
                <AnimatedCounter
                  value={metric.value}
                  suffix={metric.suffix}
                  delay={index * 200}
                />
              </div>
              <h3 className="mb-1 text-xl font-semibold text-gray-900">
                {metric.label}
              </h3>
              <p className="text-sm text-gray-600">{metric.sublabel}</p>
            </motion.div>
          ))}
        </div>

        <motion.div
          className="relative mb-24"
          initial={{ opacity: 0, scale: 0.9 }}
          whileInView={{ opacity: 1, scale: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-cyan-500 to-blue-600 p-12 text-white">
            <div className="absolute inset-0 opacity-20">
              <svg className="h-full w-full">
                {[...Array(6)].map((_, i) => (
                  <motion.circle
                    key={i}
                    cx={`${20 + i * 15}%`}
                    cy={`${30 + (i % 2) * 40}%`}
                    r="2"
                    fill="white"
                    initial={{ scale: 0 }}
                    animate={{ scale: [1, 1.5, 1] }}
                    transition={{
                      duration: 3,
                      delay: i * 0.5,
                      repeat: Infinity,
                      repeatType: "loop",
                    }}
                  />
                ))}
                {[...Array(5)].map((_, i) => (
                  <motion.line
                    key={`line-${i}`}
                    x1={`${20 + i * 15}%`}
                    y1={`${30 + (i % 2) * 40}%`}
                    x2={`${35 + i * 15}%`}
                    y2={`${50 - (i % 2) * 20}%`}
                    stroke="white"
                    strokeWidth="0.5"
                    opacity="0.3"
                    initial={{ pathLength: 0 }}
                    animate={{ pathLength: 1 }}
                    transition={{
                      duration: 2,
                      delay: i * 0.3,
                      repeat: Infinity,
                      repeatType: "reverse",
                    }}
                  />
                ))}
              </svg>
            </div>

            <div className="relative z-10 grid items-center gap-12 lg:grid-cols-2">
              <div>
                <h3 className="mb-4 text-3xl font-semibold md:text-4xl">
                  Connecting care teams across institutions
                </h3>
                <p className="mb-6 text-lg text-white/90">
                  Our platform seamlessly integrates with existing EMR systems,
                  creating a unified oncology intelligence layer that enhances
                  clinical decision-making without disrupting workflows.
                </p>
                <div className="flex flex-wrap gap-3">
                  {[
                    "FHIR Compatible",
                    "HL7 Integration",
                    "ABDM Ready",
                    "ISO 27001",
                  ].map((badge) => (
                    <span
                      key={badge}
                      className="rounded-full bg-white/20 px-4 py-2 text-sm font-medium backdrop-blur-sm"
                    >
                      {badge}
                    </span>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                {[
                  { label: "Hospitals", value: "24" },
                  { label: "Oncologists", value: "180+" },
                  { label: "Daily Cases", value: "450" },
                  { label: "Data Points", value: "2.3M" },
                ].map((stat, index) => (
                  <motion.div
                    key={index}
                    className="rounded-xl bg-white/10 p-6 backdrop-blur-sm"
                    whileHover={{ scale: 1.05 }}
                  >
                    <div className="mb-1 text-3xl font-bold">{stat.value}</div>
                    <div className="text-sm text-white/80">{stat.label}</div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </motion.div>

        <div className="mb-16">
          <motion.h3
            className="mb-12 text-center text-3xl font-semibold text-gray-900"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
          >
            Trusted by leading oncology teams
          </motion.h3>

          <div className="grid gap-8 md:grid-cols-3">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                className="rounded-2xl bg-white p-8 shadow-lg"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ y: -5, transition: { duration: 0.2 } }}
              >
                <div className="mb-6">
                  <svg
                    className="h-8 w-8 text-cyan-500 opacity-50"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                  </svg>
                </div>
                <p className="mb-6 italic text-gray-700">
                  "{testimonial.quote}"
                </p>
                <div className="flex items-center gap-3">
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-cyan-500 to-blue-600 font-semibold text-white">
                    {testimonial.avatar}
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">
                      {testimonial.author}
                    </div>
                    <div className="text-sm text-gray-600">
                      {testimonial.role}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <h3 className="mb-4 text-2xl font-semibold text-gray-900 md:text-3xl">
            Ready to transform your oncology workflow?
          </h3>
          <p className="mx-auto mb-8 max-w-2xl text-lg text-gray-600">
            Join leading institutions already using Aura to deliver better
            patient outcomes
          </p>
          <div className="flex justify-center gap-4">
            <motion.button
              className="rounded-xl bg-gradient-to-r from-cyan-600 to-blue-600 px-8 py-4 font-semibold text-white shadow-lg transition-shadow hover:shadow-xl"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Schedule Demo
            </motion.button>
            <motion.button
              className="rounded-xl border border-gray-200 bg-white px-8 py-4 font-semibold text-gray-900 shadow-lg transition-shadow hover:shadow-xl"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              View Case Studies
            </motion.button>
          </div>
        </motion.div>
      </motion.div>
    </section>
  );
});

ValuesSection.displayName = "ValuesSection";

export default ValuesSection;
