import { <PERSON><PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import { Calendar, Mail, Rocket, Target, Users } from "lucide-react";
import React, { memo } from "react";

interface Milestone {
  phase: string;
  title: string;
  description: string;
  status: string;
}

const milestones: Milestone[] = [
  {
    phase: "Current",
    title: "Proof of Concept",
    description:
      "Validating core interoperability concepts with initial prototypes",
    status: "active",
  },
  {
    phase: "Next",
    title: "MVP Development",
    description: "Building production-ready platform with design partners",
    status: "upcoming",
  },
  {
    phase: "Future",
    title: "Pilot Program",
    description: "Live deployment with select healthcare institutions",
    status: "planned",
  },
];

const About: React.FC = memo(() => {
  return (
    <section id="about" className="bg-gradient-subtle py-20">
      <div className="container-optimized space-y-20">
        {/* Header */}
        <motion.div
          className="space-y-4 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <div className="inline-flex items-center gap-2 rounded-full bg-accent/10 px-4 py-2 text-sm font-medium text-accent">
            <div className="h-2 w-2 animate-pulse rounded-full bg-accent"></div>
            About OncoConnect
          </div>
          <h2 className="lg:text-display-large mb-4 text-heading-1 text-foreground">
            Pioneering the Future of
            <span className="block text-primary">Cancer Care</span>
          </h2>
          <p className="mx-auto max-w-3xl text-xl text-muted-foreground">
            We're on a mission to transform oncology care in India through
            intelligent data interoperability, enabling evidence-based decisions
            that save lives.
          </p>
        </motion.div>

        {/* Mission & Vision Cards */}
        <div className="grid items-center gap-12 lg:grid-cols-2">
          <div className="space-y-8">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="space-y-6"
            >
              <h3 className="text-2xl font-bold text-foreground">
                Our Mission
              </h3>
              <p className="text-body-lg leading-relaxed text-muted-foreground">
                To bridge the critical gaps in India's fragmented healthcare
                data ecosystem by creating an intelligent interoperability layer
                that enables comprehensive, evidence-based cancer care
                decisions.
              </p>
              <p className="text-body-lg leading-relaxed text-muted-foreground">
                We believe that every cancer patient deserves access to their
                complete medical history and the latest evidence-based treatment
                insights, regardless of which hospital or system they visit.
              </p>
            </motion.div>

            <motion.div
              className="grid gap-6 md:grid-cols-2"
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="rounded-xl border bg-card p-6 transition-shadow hover:shadow-lg">
                <Target className="mb-3 h-8 w-8 text-primary" />
                <h4 className="mb-2 font-semibold text-foreground">
                  Our Vision
                </h4>
                <p className="text-sm text-muted-foreground">
                  A connected healthcare ecosystem where every oncologist has
                  instant access to comprehensive patient data and
                  evidence-based insights.
                </p>
              </div>
              <div className="rounded-xl border bg-card p-6 transition-shadow hover:shadow-lg">
                <Users className="mb-3 h-8 w-8 text-accent" />
                <h4 className="mb-2 font-semibold text-foreground">
                  Our Impact
                </h4>
                <p className="text-sm text-muted-foreground">
                  Empowering healthcare professionals with the tools and data
                  they need to deliver precision oncology care across India.
                </p>
              </div>
            </motion.div>
          </div>

          {/* Roadmap */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="relative"
          >
            <div className="rounded-2xl border bg-card p-8 shadow-lg">
              <div className="mb-8 text-center">
                <Rocket className="mx-auto mb-4 h-12 w-12 text-primary" />
                <h4 className="text-2xl font-bold text-foreground">
                  Development Roadmap
                </h4>
                <p className="text-muted-foreground">
                  Our journey from concept to deployment
                </p>
              </div>

              <div className="space-y-6">
                {milestones.map((milestone, index) => (
                  <div key={index} className="relative">
                    <div
                      className={`flex items-start space-x-4 rounded-lg p-4 transition-all duration-300 hover:scale-[1.02] ${
                        milestone.status === "active"
                          ? "border border-primary/20 bg-primary-lighter"
                          : milestone.status === "upcoming"
                            ? "border border-accent/20 bg-accent-light"
                            : "border border-border bg-muted"
                      }`}
                    >
                      <div
                        className={`mt-2 h-3 w-3 flex-shrink-0 rounded-full ${
                          milestone.status === "active"
                            ? "bg-primary"
                            : milestone.status === "upcoming"
                              ? "bg-accent"
                              : "bg-muted-foreground"
                        }`}
                      />
                      <div className="flex-1">
                        <div className="mb-1 flex items-center gap-2">
                          <span className="text-xs font-medium uppercase tracking-wide text-muted-foreground">
                            {milestone.phase}
                          </span>
                        </div>
                        <h5 className="mb-1 font-semibold text-foreground">
                          {milestone.title}
                        </h5>
                        <p className="text-sm text-muted-foreground">
                          {milestone.description}
                        </p>
                      </div>
                    </div>

                    {index < milestones.length - 1 && (
                      <div className="absolute left-6 top-16 h-6 w-px bg-border"></div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="rounded-2xl bg-gradient-hero p-8 text-center text-white"
        >
          <h3 className="mb-4 text-2xl font-bold">
            Ready to Transform Cancer Care Together?
          </h3>
          <p className="mx-auto mb-8 max-w-3xl text-lg opacity-90">
            Join us in building the future of evidence-based oncology care.
            Whether you're a healthcare institution, technology partner, or
            investor, we'd love to hear from you.
          </p>

          <div className="flex flex-col justify-center gap-4 sm:flex-row">
            <Button
              variant="outline"
              size="lg"
              className="border-white/20 bg-white/10 text-white hover:bg-white/20"
            >
              <Calendar className="mr-2 h-5 w-5" />
              Schedule Demo
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-white/20 bg-white/10 text-white hover:bg-white/20"
            >
              <Mail className="mr-2 h-5 w-5" />
              Partnership Inquiry
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  );
});

About.displayName = "About";

export default About;
