@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Open+Sans:wght@300;400;500;600;700&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --text-xs: 0.625rem;
    --text-sm: 0.75rem;
    --text-base: 0.875rem;
    --text-lg: 1rem;
    --text-xl: 1.125rem;
    --text-2xl: 1.25rem;
    --text-3xl: 1.5rem;
    --text-4xl: 1.875rem;
    --text-5xl: 2.25rem;
    --text-6xl: 2.75rem;
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 3.5rem;
    --space-20: 4rem;
    --space-24: 4.5rem;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;
    --brand-primary: 220 85% 58%;
    --brand-primary-light: 220 85% 68%;
    --brand-primary-lighter: 220 85% 85%;
    --brand-primary-dark: 220 85% 48%;
    --brand-primary-darker: 220 85% 35%;
    --brand-primary-foreground: 0 0% 100%;
    --brand-secondary: 45 90% 60%;
    --brand-secondary-light: 45 90% 70%;
    --brand-secondary-dark: 45 90% 50%;
    --brand-secondary-foreground: 0 0% 15%;
    --medical-critical: 0 75% 55%;
    --medical-critical-light: 0 75% 95%;
    --medical-critical-foreground: 0 0% 100%;
    --medical-warning: 45 100% 50%;
    --medical-warning-light: 45 100% 95%;
    --medical-warning-foreground: 0 0% 15%;
    --medical-success: 140 65% 42%;
    --medical-success-light: 140 65% 95%;
    --medical-success-foreground: 0 0% 100%;
    --medical-info: 210 90% 55%;
    --medical-info-light: 210 90% 95%;
    --medical-info-foreground: 0 0% 100%;
    --medical-neutral: 200 15% 60%;
    --medical-neutral-light: 200 15% 95%;
    --medical-neutral-foreground: 0 0% 15%;
    --trust-verified: 140 70% 40%;
    --trust-secure: 220 85% 50%;
    --trust-compliant: 260 60% 50%;
    --neutral-50: 220 20% 98%;
    --neutral-100: 220 15% 95%;
    --neutral-200: 220 10% 90%;
    --neutral-300: 220 8% 80%;
    --neutral-400: 220 6% 65%;
    --neutral-500: 220 5% 50%;
    --neutral-600: 220 8% 35%;
    --neutral-700: 220 12% 25%;
    --neutral-800: 220 15% 15%;
    --neutral-900: 220 20% 8%;
    --surface-warm: 35 50% 98%;
    --surface-cool: 220 30% 98%;
    --surface-soft: 45 25% 97%;
    --surface-elevated: 0 0% 100%;
    --cta-primary: 220 85% 58%;
    --cta-primary-hover: 220 85% 48%;
    --cta-secondary: 45 85% 55%;
    --cta-success: 140 65% 42%;
    --cta-danger: 0 75% 55%;
    --interactive-hover: 220 85% 95%;
    --interactive-active: 220 85% 90%;
    --interactive-focus: 220 85% 58%;
    --interactive-disabled: 220 5% 80%;
    --text-primary-contrast: 220 20% 8%;
    --text-secondary-contrast: 220 15% 25%;
    --text-muted-contrast: 220 10% 42%;
    --focus-ring: 220 100% 60%;
    --error-indication: 0 100% 35%;
    --success-indication: 140 100% 25%;
    --background: 0 0% 100%;
    --foreground: 220 20% 8%;
    --card: 0 0% 100%;
    --card-foreground: 220 20% 8%;
    --popover: 0 0% 100%;
    --popover-foreground: 220 20% 8%;
    --primary: 220 85% 58%;
    --primary-foreground: 0 0% 100%;
    --primary-light: 220 85% 68%;
    --primary-lighter: 220 85% 85%;
    --primary-dark: 220 85% 48%;
    --secondary: 220 15% 95%;
    --secondary-foreground: 220 20% 8%;
    --secondary-accent: 45 90% 60%;
    --muted: 220 15% 95%;
    --muted-foreground: 220 10% 42%;
    --accent: 260 60% 50%;
    --accent-foreground: 0 0% 100%;
    --accent-light: 260 60% 95%;
    --accent-secondary: 45 85% 55%;
    --success: 140 65% 42%;
    --success-foreground: 0 0% 100%;
    --success-light: 140 65% 95%;
    --destructive: 0 75% 55%;
    --destructive-foreground: 0 0% 100%;
    --border: 220 10% 90%;
    --input: 220 15% 95%;
    --ring: 220 85% 58%;
    --gradient-medical: linear-gradient(
      135deg,
      hsl(210 90% 55%) 0%,
      hsl(140 65% 42%) 100%
    );
    --gradient-trust: linear-gradient(
      135deg,
      hsl(220 85% 50%) 0%,
      hsl(140 70% 40%) 100%
    );
    --gradient-hero-medical: linear-gradient(
      135deg,
      hsl(220 85% 58%) 0%,
      hsl(210 90% 55%) 50%,
      hsl(140 65% 42%) 100%
    );
    --gradient-purple: linear-gradient(
      135deg,
      #e6e6fa 0%,
      #dda0dd 50%,
      #da70d6 100%
    );
    --gradient-orange: linear-gradient(
      135deg,
      #fff8dc 0%,
      #ffe4b5 30%,
      #ffa500 70%,
      #ff8c00 100%
    );
    --gradient-blue: linear-gradient(
      135deg,
      #f0f8ff 0%,
      #b0e0e6 40%,
      #87ceeb 100%
    );
    --gradient-green: linear-gradient(
      135deg,
      #f0fff0 0%,
      #98fb98 50%,
      #90ee90 100%
    );
    --gradient-hero: linear-gradient(
      135deg,
      hsl(220 85% 58%) 0%,
      hsl(260 60% 50%) 100%
    );
    --gradient-soft: radial-gradient(
      ellipse at center,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(248, 248, 248, 0.8) 100%
    );
    --shadow-soft: 0 4px 20px rgba(34, 60, 120, 0.08);
    --shadow-medium: 0 8px 40px rgba(34, 60, 120, 0.12);
    --shadow-organic: 0 20px 60px rgba(34, 60, 120, 0.15);
    --transition-organic: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --radius: 0.75rem;
    --sidebar-background: 220 15% 98%;
    --sidebar-foreground: 220 20% 8%;
    --sidebar-primary: 220 85% 58%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 220 15% 95%;
    --sidebar-accent-foreground: 220 20% 8%;
    --sidebar-border: 220 10% 90%;
    --sidebar-ring: 220 85% 58%;
  }
  .dark {
    --background: 220 15% 8%;
    --foreground: 220 15% 95%;
    --card: 220 12% 12%;
    --card-foreground: 220 15% 95%;
    --popover: 220 12% 12%;
    --popover-foreground: 220 15% 95%;
    --brand-primary: 220 85% 68%;
    --brand-primary-light: 220 85% 78%;
    --brand-primary-lighter: 220 85% 25%;
    --brand-primary-dark: 220 85% 58%;
    --brand-primary-darker: 220 85% 45%;
    --primary: 220 85% 68%;
    --primary-foreground: 220 15% 8%;
    --primary-light: 220 85% 78%;
    --primary-lighter: 220 85% 25%;
    --primary-dark: 220 85% 58%;
    --secondary: 220 12% 15%;
    --secondary-foreground: 220 15% 95%;
    --muted: 220 12% 15%;
    --muted-foreground: 220 8% 65%;
    --accent: 260 60% 70%;
    --accent-foreground: 220 15% 8%;
    --accent-light: 260 60% 20%;
    --medical-critical: 0 75% 65%;
    --medical-critical-light: 0 75% 20%;
    --medical-warning: 45 100% 60%;
    --medical-warning-light: 45 100% 15%;
    --medical-success: 140 55% 55%;
    --medical-success-light: 140 55% 15%;
    --medical-info: 210 90% 65%;
    --medical-info-light: 210 90% 20%;
    --success: 140 55% 55%;
    --success-foreground: 220 15% 8%;
    --success-light: 140 55% 15%;
    --destructive: 0 75% 65%;
    --destructive-foreground: 220 15% 8%;
    --border: 220 12% 20%;
    --input: 220 12% 20%;
    --ring: 220 85% 68%;
    --gradient-medical: linear-gradient(
      135deg,
      hsl(210 90% 65%) 0%,
      hsl(140 55% 55%) 100%
    );
    --gradient-trust: linear-gradient(
      135deg,
      hsl(220 85% 68%) 0%,
      hsl(140 55% 55%) 100%
    );
    --gradient-hero-medical: linear-gradient(
      135deg,
      hsl(220 85% 68%) 0%,
      hsl(210 90% 65%) 50%,
      hsl(140 55% 55%) 100%
    );
    --gradient-purple: linear-gradient(
      135deg,
      #2d1b69 0%,
      #6a4c93 50%,
      #8b5a96 100%
    );
    --gradient-orange: linear-gradient(
      135deg,
      #4a3728 0%,
      #8b4513 30%,
      #cd853f 70%,
      #d2691e 100%
    );
    --gradient-blue: linear-gradient(
      135deg,
      #1c2331 0%,
      #2c4f70 40%,
      #4682b4 100%
    );
    --gradient-green: linear-gradient(
      135deg,
      #1c3a1c 0%,
      #2e5c2e 50%,
      #4a7c4a 100%
    );
    --gradient-hero: linear-gradient(
      135deg,
      hsl(220 85% 68%) 0%,
      hsl(260 60% 70%) 100%
    );
    --gradient-soft: radial-gradient(
      ellipse at center,
      rgba(24, 24, 24, 0.9) 0%,
      rgba(16, 16, 16, 0.8) 100%
    );
    --shadow-soft: 0 4px 20px rgba(0, 0, 0, 0.4);
    --shadow-medium: 0 8px 40px rgba(0, 0, 0, 0.5);
    --shadow-organic: 0 20px 60px rgba(0, 0, 0, 0.6);
    --sidebar-background: 220 15% 8%;
    --sidebar-foreground: 220 15% 95%;
    --sidebar-primary: 220 85% 68%;
    --sidebar-primary-foreground: 220 15% 8%;
    --sidebar-accent: 220 12% 15%;
    --sidebar-accent-foreground: 220 15% 95%;
    --sidebar-border: 220 12% 20%;
    --sidebar-ring: 220 85% 68%;
  }
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family:
      "Inter",
      system-ui,
      -apple-system,
      BlinkMacSystemFont,
      "Segoe UI",
      sans-serif;
    font-feature-settings:
      "kern" 1,
      "liga" 1,
      "calt" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}
.organic-blob {
  position: absolute;
  border-radius: 50% 40% 60% 30%;
  filter: blur(40px);
  opacity: 0.15;
  animation: float 20s ease-in-out infinite;
}
.organic-blob:nth-child(2) {
  animation-delay: -5s;
}
.organic-blob:nth-child(3) {
  animation-delay: -10s;
}
.organic-blob:nth-child(4) {
  animation-delay: -15s;
}
@keyframes float {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  33% {
    transform: translateY(-30px) rotate(120deg);
  }
  66% {
    transform: translateY(30px) rotate(240deg);
  }
}
@layer components {
  .text-display-hero {
    @apply text-display-lg font-light tracking-tight text-foreground md:text-display-lg;
    font-variation-settings: "wght" 300;
  }
  .text-display-large {
    @apply text-display-md font-light tracking-tight text-foreground md:text-display-md;
    font-variation-settings: "wght" 300;
  }
  .text-display-small {
    @apply text-display-sm font-light tracking-tight text-foreground md:text-display-sm;
    font-variation-settings: "wght" 300;
    font-variation-settings: "wght" 300;
  }
  .text-heading-major {
    @apply text-heading-1 font-normal tracking-tight text-foreground md:text-heading-1;
    font-variation-settings: "wght" 400;
  }
  .text-heading-section {
    @apply text-heading-2 font-normal tracking-tight text-foreground md:text-heading-2;
    font-variation-settings: "wght" 400;
  }
  .text-heading-subsection {
    @apply text-heading-3 font-medium tracking-tight text-foreground md:text-heading-3;
    font-variation-settings: "wght" 500;
  }
  .text-heading-card {
    @apply text-heading-4 font-medium tracking-tight text-foreground md:text-heading-4;
    font-variation-settings: "wght" 500;
  }
  .text-heading-minor {
    @apply text-heading-5 font-medium tracking-tight text-foreground md:text-heading-5;
    font-variation-settings: "wght" 500;
  }
  .text-heading-small {
    @apply text-heading-6 font-semibold tracking-tight text-foreground md:text-heading-6;
    font-variation-settings: "wght" 600;
  }
  .text-body-lead {
    @apply text-body-lg font-normal leading-relaxed text-foreground md:text-body-lg;
    font-variation-settings: "wght" 400;
  }
  .text-body-large {
    @apply text-body-md font-normal leading-relaxed text-foreground md:text-body-md;
    font-variation-settings: "wght" 400;
  }
  .text-body-default {
    @apply text-body-sm font-normal leading-relaxed text-foreground md:text-body-sm;
    font-variation-settings: "wght" 400;
  }
  .text-body-small {
    @apply text-body-xs font-normal leading-relaxed text-muted-foreground md:text-body-xs;
    font-variation-settings: "wght" 400;
  }
  .text-caption {
    @apply text-caption font-medium uppercase tracking-wide text-muted-foreground;
    font-variation-settings: "wght" 500;
  }
  .text-caption-default {
    @apply text-caption font-normal text-muted-foreground;
    font-variation-settings: "wght" 400;
  }
  .text-nav-item {
    @apply text-body-xs font-medium text-muted-foreground hover:text-foreground md:text-body-sm;
    font-variation-settings: "wght" 500;
    transition: var(--transition-organic);
  }
  .text-button {
    @apply text-body-xs font-medium tracking-wide md:text-body-sm;
    font-variation-settings: "wght" 500;
  }
  .text-button-large {
    @apply text-body-sm font-medium tracking-wide md:text-body-md;
    font-variation-settings: "wght" 500;
  }
  .text-form-label {
    @apply text-body-xs font-medium text-foreground;
    font-variation-settings: "wght" 500;
  }
  .text-card-title {
    @apply text-heading-5 font-semibold text-foreground md:text-heading-4;
    font-variation-settings: "wght" 600;
  }
  .text-metric {
    @apply text-heading-2 font-bold tabular-nums text-primary md:text-heading-1;
    font-variation-settings: "wght" 700;
  }
  .text-metric-label {
    @apply text-body-xs font-medium uppercase tracking-wide text-muted-foreground;
    font-variation-settings: "wght" 500;
  }
  .text-medical-term {
    @apply text-body-sm font-medium tracking-wide text-foreground;
    font-variation-settings: "wght" 500;
  }
  .text-clinical-data {
    @apply font-mono text-body-sm tabular-nums text-foreground;
    font-family:
      "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas,
      "Courier New", monospace;
  }
  .text-medical-critical {
    color: hsl(var(--medical-critical));
  }
  .text-medical-warning {
    color: hsl(var(--medical-warning));
  }
  .text-medical-success {
    color: hsl(var(--medical-success));
  }
  .text-medical-info {
    color: hsl(var(--medical-info));
  }
  img,
  video,
  iframe,
  canvas,
  svg,
  object {
    max-width: 100%;
    height: auto;
    display: block;
  }
  *,
  *::before,
  *::after {
    box-sizing: border-box;
  }
  body {
    overflow-x: hidden;
  }
  *:focus-visible {
    outline: 2px solid hsl(var(--focus-ring));
    outline-offset: 2px;
    border-radius: 4px;
  }
  .skip-to-main {
    position: absolute;
    top: -40px;
    left: 6px;
    background: hsl(var(--background));
    color: hsl(var(--foreground));
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    transition: top 0.3s;
  }
  .skip-to-main:focus {
    top: 6px;
  }
  @media (max-width: 600px) {
    html {
      font-size: 100%;
    }
  }
  @media (min-width: 601px) and (max-width: 1024px) {
    html {
      font-size: 100%;
    }
  }
  @media (min-width: 1025px) {
    html {
      font-size: 100%;
    }
  }
  @media (max-width: 640px) {
    .text-display-hero {
      @apply text-heading-2;
    }
    .text-display-large {
      @apply text-heading-3;
    }
    .text-heading-major {
      @apply text-heading-4;
    }
    .text-heading-section {
      @apply text-heading-5;
    }
  }
  @media (min-width: 1280px) {
    .text-display-hero {
      font-size: 3.5rem;
      line-height: 4rem;
    }
    .text-display-large {
      font-size: 2.75rem;
      line-height: 3.25rem;
    }
  }
}
@layer components {
  .status-critical {
    @apply rounded-lg border border-medical-critical/20 bg-medical-critical/10 px-3 py-1 text-sm font-medium text-medical-critical;
  }
  .status-warning {
    @apply rounded-lg border border-medical-warning/20 bg-medical-warning/10 px-3 py-1 text-sm font-medium text-medical-warning;
  }
  .status-success {
    @apply rounded-lg border border-medical-success/20 bg-medical-success/10 px-3 py-1 text-sm font-medium text-medical-success;
  }
  .status-info {
    @apply rounded-lg border border-medical-info/20 bg-medical-info/10 px-3 py-1 text-sm font-medium text-medical-info;
  }
  .trust-badge {
    @apply rounded-full border border-trust-verified/20 bg-trust-verified/10 px-4 py-2 text-sm font-semibold text-trust-verified;
  }
}
@layer utilities {
  .text-display-hero {
    font-size: clamp(1.75rem, 5vw, 3.5rem);
    line-height: var(--leading-tight);
    font-weight: 600;
    letter-spacing: -0.02em;
  }
  .text-display-large {
    font-size: clamp(1.25rem, 2.5vw, 2rem);
    line-height: var(--leading-tight);
    font-weight: 300;
    letter-spacing: -0.01em;
  }
  .text-heading-1 {
    font-size: clamp(1.125rem, 2.25vw, 1.75rem);
    line-height: var(--leading-snug);
    font-weight: 600;
  }
  .text-heading-2 {
    font-size: clamp(1rem, 1.75vw, 1.375rem);
    line-height: var(--leading-snug);
    font-weight: 600;
  }
  .text-heading-3 {
    font-size: clamp(0.875rem, 1.25vw, 1.125rem);
    line-height: var(--leading-snug);
    font-weight: 600;
  }
  .text-body-large {
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
    font-weight: 400;
  }
  .text-body-base {
    font-size: var(--text-base);
    line-height: var(--leading-normal);
    font-weight: 400;
  }
  .text-body-small {
    font-size: var(--text-sm);
    line-height: var(--leading-normal);
    font-weight: 400;
  }
  .space-y-optimal > * + * {
    margin-top: var(--space-6);
  }
  .space-y-tight > * + * {
    margin-top: var(--space-4);
  }
  .space-y-loose > * + * {
    margin-top: var(--space-8);
  }
  .container-optimized {
    max-width: 1100px;
    margin: 0 auto;
    padding-left: var(--space-4);
    padding-right: var(--space-4);
  }
  @media (max-width: 768px) {
    .container-optimized {
      padding-left: var(--space-3);
      padding-right: var(--space-3);
    }
  }
  @media (min-width: 1200px) {
    .container-optimized {
      padding-left: var(--space-6);
      padding-right: var(--space-6);
    }
  }
}
@media (max-width: 768px) {
  #hero {
    --hero-axis-x: 50vw !important;
    --card-w: min(85vw, 280px) !important;
    --branch-offset: 48px !important;
    --left-card-offset: -80px !important;
    --flow-h: 50vh !important;
  }
  .container {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
  }
  .flow-hero-card {
    width: 100% !important;
    max-width: 85vw !important;
  }
  .aura-stack-section {
    height: auto !important;
    min-height: 100vh;
  }
  .will-change-transform-opacity {
    will-change: auto !important;
  }
  .frame-container {
    padding: 1rem !important;
  }
  [data-section] {
    transform: translateY(20px) !important;
  }
  .overlay-animation {
    display: none !important;
  }
  .text-display-hero {
    font-size: clamp(1.375rem, 3.5vw, 1.75rem) !important;
  }
  .text-display-large {
    font-size: clamp(1.125rem, 3vw, 1.375rem) !important;
  }
  .text-heading-1 {
    font-size: clamp(1rem, 2.5vw, 1.25rem) !important;
  }
  .text-heading-2 {
    font-size: clamp(0.875rem, 2vw, 1.125rem) !important;
  }
  .text-body-large {
    font-size: 0.875rem !important;
  }
  .grid.lg\:grid-cols-2 {
    grid-template-columns: 1fr !important;
    gap: 2rem !important;
  }
  [aria-hidden="true"]:has(svg) {
    height: 20vh !important;
  }
  .ScrollTrigger-pin {
    position: relative !important;
  }
  .container {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
  }
  .container-optimized {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
  }
  @media (max-width: 480px) {
    #hero {
      --flow-h: 50vh !important;
    }
    .oncology-flow-section {
      display: none !important;
    }
    .text-display-hero {
      font-size: 1.5rem !important;
    }
    .text-display-large {
      font-size: 1.25rem !important;
    }
  }
}
@media (min-width: 641px) and (max-width: 1024px) {
  .aura-stack-section {
    min-height: 80vh;
  }
  .overlay-animation {
    opacity: 0.6 !important;
  }
  .text-display-hero {
    font-size: clamp(1.75rem, 5vw, 2.25rem) !important;
  }
  .text-display-large {
    font-size: clamp(1.5rem, 4vw, 1.875rem) !important;
  }
  .frame-container {
    padding: 2rem !important;
  }
  [aria-hidden="true"]:has(svg) {
    height: 30vh !important;
  }
}
@media (hover: none) and (pointer: coarse) {
  *:hover {
    transform: none !important;
  }
  button,
  a,
  [role="button"] {
    min-height: 44px;
    min-width: 44px;
    padding: 0.75rem 1rem;
  }
  .parallax {
    transform: none !important;
  }
  [data-scroll-trigger] {
    animation-duration: 0.3s !important;
  }
  input,
  select,
  textarea {
    font-size: 16px !important;
  }
  body {
    -webkit-overflow-scrolling: touch;
  }
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .lazyload {
    transform: translateZ(0);
    backface-visibility: hidden;
  }
}
@media (max-width: 896px) and (orientation: landscape) {
  .min-h-screen {
    min-height: 100vw !important;
  }
  .py-24 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .landscape\:hidden {
    display: none !important;
  }
}
@media print {
  .will-change-transform,
  .will-change-opacity,
  .overlay-animation,
  button,
  input,
  video,
  [role="progressbar"] {
    display: none !important;
  }
  * {
    opacity: 1 !important;
    visibility: visible !important;
    transform: none !important;
  }
  body {
    font-size: 12pt !important;
    color: black !important;
    background: white !important;
  }
}
@media (prefers-color-scheme: dark) {
  .dark\:invert {
    filter: invert(1);
  }
}
.will-change-transform {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}
.will-change-opacity {
  will-change: opacity;
}
.will-change-transform-opacity {
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
}
[data-scroll-container] {
  will-change: scroll-position;
}
[data-section] {
  will-change: transform, opacity;
  transform: translateZ(0);
}
.frame-container {
  will-change: transform, opacity, visibility;
  transform: translateZ(0);
  backface-visibility: hidden;
}
.animated-svg {
  will-change: transform;
  transform: translateZ(0);
}
.animated-path {
  will-change: stroke-dashoffset, opacity;
}
.badge {
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
}
.overlay-animation {
  will-change: opacity;
  pointer-events: none;
}
.perf-indicator {
  position: fixed;
  bottom: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 10px;
  font-size: 12px;
  font-family: monospace;
  border-radius: 4px;
  z-index: 9999;
  pointer-events: none;
}
html {
  scroll-behavior: smooth;
}
.promote-layer {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
}
@layer utilities {
  @keyframes organic-float {
    0%,
    100% {
      transform: translateY(0) rotate(0deg) scale(1);
      opacity: 0.6;
    }
    25% {
      transform: translateY(-15px) rotate(3deg) scale(1.02);
      opacity: 0.8;
    }
    50% {
      transform: translateY(-25px) rotate(-2deg) scale(1.05);
      opacity: 1;
    }
    75% {
      transform: translateY(-10px) rotate(1deg) scale(1.02);
      opacity: 0.9;
    }
  }
  @keyframes pulse {
    0%,
    100% {
      opacity: 0.4;
      transform: scale(1);
    }
    50% {
      opacity: 0.6;
      transform: scale(1.05);
    }
  }
  @keyframes gradientShift {
    0%,
    100% {
      transform: translate3d(0, 0, 0) scale(1);
    }
    50% {
      transform: translate3d(-1%, 0.5%, 0) scale(1.01);
    }
  }
  @keyframes driftSlow {
    0%,
    100% {
      transform: translate3d(0, 0, 0) rotate(0deg);
    }
    50% {
      transform: translate3d(2%, -1%, 0) rotate(4deg);
    }
  }
  @keyframes organic-pulse {
    0%,
    100% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.05);
      opacity: 0.8;
    }
  }
  @keyframes organic-breathe {
    0%,
    100% {
      transform: scale(1) rotate(0deg);
      filter: blur(0);
    }
    50% {
      transform: scale(1.1) rotate(2deg);
      filter: blur(1px);
    }
  }
  @keyframes liquid-morph {
    0% {
      border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    }
    25% {
      border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
    }
    50% {
      border-radius: 50% 60% 30% 60% / 30% 60% 70% 40%;
    }
    75% {
      border-radius: 60% 40% 60% 40% / 70% 30% 60% 40%;
    }
    100% {
      border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    }
  }
  @keyframes fade-up {
    from {
      opacity: 0;
      transform: translateY(40px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }
  @keyframes fade-down {
    from {
      opacity: 0;
      transform: translateY(-40px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }
  @keyframes fade-left {
    from {
      opacity: 0;
      transform: translateX(-40px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateX(0) scale(1);
    }
  }
  @keyframes fade-right {
    from {
      opacity: 0;
      transform: translateX(40px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateX(0) scale(1);
    }
  }
  @keyframes scale-in {
    from {
      opacity: 0;
      transform: scale(0.8);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
  @keyframes rotate-in {
    from {
      opacity: 0;
      transform: rotate(-10deg) scale(0.9);
    }
    to {
      opacity: 1;
      transform: rotate(0deg) scale(1);
    }
  }
  @keyframes button-press {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(0.95);
    }
    100% {
      transform: scale(1);
    }
  }
  @keyframes card-lift {
    from {
      transform: translateY(0);
      box-shadow: 0 4px 20px rgba(34, 60, 120, 0.08);
    }
    to {
      transform: translateY(-8px);
      box-shadow: 0 20px 60px rgba(34, 60, 120, 0.15);
    }
  }
  @keyframes input-focus {
    from {
      transform: scale(1);
      box-shadow: 0 0 0 0 hsl(var(--ring));
    }
    to {
      transform: scale(1.02);
      box-shadow: 0 0 0 3px hsl(var(--ring) / 0.3);
    }
  }
  @keyframes ripple {
    0% {
      transform: scale(0);
      opacity: 0.6;
    }
    100% {
      transform: scale(4);
      opacity: 0;
    }
  }
  @keyframes stagger-fade-in {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  @keyframes wave {
    0%,
    40%,
    100% {
      transform: scaleY(0.4);
    }
    20% {
      transform: scaleY(1);
    }
  }
  @keyframes shimmer {
    from {
      background-position: 200% 0;
    }
    to {
      background-position: -200% 0;
    }
  }
  .animate-organic-float {
    animation: organic-float 8s ease-in-out infinite;
  }
  .animate-organic-pulse {
    animation: organic-pulse 3s ease-in-out infinite;
  }
  .animate-organic-breathe {
    animation: organic-breathe 4s ease-in-out infinite;
  }
  .animate-liquid-morph {
    animation: liquid-morph 10s ease-in-out infinite;
  }
  .animate-fade-up {
    animation: fade-up 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }
  .animate-fade-down {
    animation: fade-down 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }
  .animate-fade-left {
    animation: fade-left 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }
  .animate-fade-right {
    animation: fade-right 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }
  .animate-scale-in {
    animation: scale-in 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }
  .animate-rotate-in {
    animation: rotate-in 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }
  .animate-button-press {
    animation: button-press 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  .animate-card-lift {
    animation: card-lift 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }
  .animate-input-focus {
    animation: input-focus 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }
  .animate-ripple {
    animation: ripple 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  .animate-stagger-1 {
    animation: stagger-fade-in 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.1s
      both;
  }
  .animate-stagger-2 {
    animation: stagger-fade-in 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s
      both;
  }
  .animate-stagger-3 {
    animation: stagger-fade-in 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s
      both;
  }
  .animate-stagger-4 {
    animation: stagger-fade-in 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.4s
      both;
  }
  .animate-stagger-5 {
    animation: stagger-fade-in 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.5s
      both;
  }
  .animate-wave {
    animation: wave 1.2s infinite ease-in-out;
  }
  .hover-lift {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  .hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(34, 60, 120, 0.12);
  }
  .hover-scale {
    transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  .hover-scale:hover {
    transform: scale(1.05);
  }
  .hover-glow {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  .hover-glow:hover {
    box-shadow: 0 0 30px hsl(var(--primary) / 0.3);
  }
  .scroll-reveal {
    opacity: 0;
    transform: translateY(40px);
    transition: none;
  }
  .scroll-reveal.animate {
    opacity: 1;
    transform: translateY(0);
  }
}
