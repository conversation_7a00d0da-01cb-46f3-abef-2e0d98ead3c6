import { motion } from "framer-motion";
import { AlertTriangle, Database, TrendingUp, Users } from "lucide-react";
import React, { memo } from "react";

interface Problem {
  icon: React.ReactNode;
  title: string;
  description: string;
  impact: string;
}

const ProblemSolution: React.FC = memo(() => {
  const problems: Problem[] = [
    {
      icon: <AlertTriangle className="h-8 w-8 text-destructive" />,
      title: "Data Fragmentation Crisis",
      description:
        "60-70% of Indian hospitals use outdated legacy systems, creating dangerous data silos that delay critical cancer diagnosis and treatment decisions.",
      impact: "916K+ deaths in 2022",
    },
    {
      icon: <TrendingUp className="h-8 w-8 text-destructive" />,
      title: "Growing Cancer Burden",
      description:
        "Cancer incidence projected to reach 1.57 million cases by 2025, with mortality rising largely due to late detection and fragmented care.",
      impact: "+12.8% increase from 2020",
    },
    {
      icon: <Users className="h-8 w-8 text-destructive" />,
      title: "Critical Workforce Gap",
      description:
        "Severe oncologist shortage with a 1:730 ratio, hampering tumor board effectiveness and multidisciplinary care coordination.",
      impact: "2,300 shortage by 2025",
    },
  ];

  return (
    <section id="solution" className="bg-background py-24">
      <div className="container-optimized">
        {/* Problem Statement */}
        <motion.div
          className="space-y-4 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <div className="inline-flex items-center gap-2 rounded-full bg-destructive/10 px-4 py-2 text-sm font-medium text-destructive">
            <div className="h-2 w-2 animate-pulse rounded-full bg-destructive"></div>
            Critical Healthcare Challenge
          </div>
          <h2 className="lg:text-display-large mb-4 text-heading-1 text-foreground">
            The Cancer Care Crisis in
            <span className="block text-primary">India</span>
          </h2>
          <p className="mx-auto max-w-3xl text-xl text-muted-foreground">
            Fragmented healthcare data systems are creating life-threatening
            delays in cancer diagnosis and treatment across India's healthcare
            infrastructure.
          </p>
        </motion.div>

        {/* Problem Cards */}
        <div className="my-16 grid gap-8 md:grid-cols-3">
          {problems.map((problem, index) => (
            <motion.div
              key={index}
              className="group relative"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <div className="bg-gradient-surface h-full rounded-3xl border border-border/50 p-8 transition-transform duration-300 hover:shadow-md group-hover:scale-105">
                <div className="relative mb-6 flex items-center justify-between">
                  <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-destructive/10 transition-transform duration-300 group-hover:scale-110">
                    {problem.icon}
                  </div>
                  <div className="absolute -right-2 -top-2 rounded-full bg-destructive px-2 py-1 text-xs font-bold text-destructive-foreground">
                    {problem.impact}
                  </div>
                </div>
                <h3 className="mb-4 text-heading-3 text-foreground">
                  {problem.title}
                </h3>
                <p className="text-body-base text-muted-foreground">
                  {problem.description}
                </p>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Solution Statement */}
        <div className="relative">
          <motion.div
            className="mb-16 space-y-4 text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <div className="bg-gradient-surface/10 inline-flex items-center gap-2 rounded-full px-4 py-2 text-sm font-medium text-foreground">
              <div className="h-2 w-2 animate-pulse rounded-full bg-gradient-hero"></div>
              Our AI Solution
            </div>
            <h2 className="lg:text-display-large mb-4 text-heading-1 text-foreground">
              FHIR-Based
              <span className="block text-primary">
                Interoperability Platform
              </span>
            </h2>
            <p className="mx-auto max-w-3xl text-xl text-muted-foreground">
              A comprehensive platform that seamlessly integrates disparate
              hospital data sources to create unified patient histories and
              generate evidence-based tumor board reports with academic
              citations.
            </p>
          </motion.div>

          <div className="grid items-center gap-16 lg:grid-cols-2">
            {/* Left Features */}
            <div className="space-y-8">
              {[
                {
                  icon: <Database className="h-8 w-8 text-primary" />,
                  title: "Unified Data Integration",
                  description:
                    "Connects EMR, PACS, LIS, HIMS, pathology reports, billing systems, and telemedicine data into a single comprehensive view.",
                },
                {
                  icon: <TrendingUp className="h-8 w-8 text-accent" />,
                  title: "Longitudinal Patient Histories",
                  description:
                    "Creates chronological patient timelines from multi-source inputs, providing complete context for informed decision-making.",
                },
                {
                  icon: <AlertTriangle className="h-8 w-8 text-success" />,
                  title: "Evidence-Based Reports",
                  description:
                    "Generates contextual tumor board reports with verifiable citations from PubMed, ClinicalTrials.gov, and academic databases.",
                },
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  className="group flex items-start space-x-6"
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <div className="bg-gradient-surface flex h-16 w-16 flex-shrink-0 items-center justify-center rounded-2xl border border-border/20 shadow-soft transition-transform duration-300 group-hover:scale-110">
                    {feature.icon}
                  </div>
                  <div>
                    <h3 className="mb-3 text-heading-3 text-foreground">
                      {feature.title}
                    </h3>
                    <p className="text-body-base text-muted-foreground">
                      {feature.description}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Right Diagram */}
            <div className="relative">
              <div className="bg-gradient-surface shadow-strong rounded-3xl border border-border/20 p-10">
                <div className="space-y-6">
                  {[
                    "EMR Systems",
                    "PACS Imaging",
                    "Lab Results (LIS)",
                    "Pathology Reports",
                  ].map((item, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between rounded-2xl border border-border/50 bg-background p-4 shadow-soft"
                    >
                      <span className="font-semibold text-foreground">
                        {item}
                      </span>
                      <div className="h-3 w-3 animate-pulse rounded-full bg-success"></div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
});

ProblemSolution.displayName = "ProblemSolution";

export default ProblemSolution;
