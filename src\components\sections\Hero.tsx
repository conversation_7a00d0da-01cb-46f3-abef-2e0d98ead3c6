import HeroBackground from "@/components/specialized/HeroBackground";
import OncologyFlow from "@/components/specialized/OncologyFlow";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
// import { postLead } from "@/lib/lead";
import React, { useCallback, useState } from "react";

const Hero: React.FC = () => {
  const [email, setEmail] = useState("");
  const [open, setOpen] = useState(false);
  const [error, setError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleWatch = useCallback(
    async (e?: React.FormEvent) => {
      e?.preventDefault();
      if (!/[^\s@]+@[^\s@]+\.[^\s@]+/.test(email)) {
        setError("Please enter a valid work email.");
        return;
      }
      setError("");
      setIsSubmitting(true);
      try {
        // await postLead({ source: "demo", email, role: "unknown" });
        setOpen(true);
      } catch {
        setError("Submission failed. Please try again.");
      } finally {
        setIsSubmitting(false);
      }
    },
    [email]
  );

  return (
    <section
      id="hero"
      className="relative min-h-screen overflow-hidden p-4"
      style={
        {
          "--hero-axis-x": "clamp(40vw, 100vw, 80vw)",
          "--card-w": "clamp(240px, 85vw, 420px)",
          "--axis-gap": "clamp(8px, 2vw, 16px)",
          "--axis-gap-right": "clamp(16px, 4vw, 32px)",
          "--axis-gap-left": "clamp(6px, 1.5vw, 12px)",
          "--branch-offset": "clamp(48px, 12vw, 112px)",
          "--top-y1": "clamp(48px, 12vh, 96px)",
          "--top-y2": "clamp(96px, 20vh, 200px)",
          "--gate-y": "clamp(144px, 28vh, 280px)",
          "--bot-y1": "clamp(192px, 36vh, 360px)",
          "--bot-y2": "clamp(240px, 48vh, 480px)",
          "--flow-h": "clamp(320px, 64vh, 580px)",
          "--card-shadow": "0 12px 40px rgba(2, 6, 23, 0.12)",
        } as React.CSSProperties
      }
    >
      <HeroBackground />

      <div className="container-optimized relative z-20" data-animate-hero-title>
        <div className="flex min-h-screen items-center py-20 lg:py-0">
          <div className="max-w-xl space-y-6 text-center lg:text-left">
            <h1
              data-animate-hero-title
              className="text-display-hero leading-tight"
            >
              <span className="block">Stop hunting</span>
              <span className="block">
                for{" "}
                <em className="font-bold italic text-primary">patient data</em>
              </span>
              <span className="block">across systems.</span>
            </h1>

            <p
              data-animate-hero-text
              className="text-body-large mx-auto max-w-lg text-muted-foreground lg:mx-0"
            >
              Get complete clinical context in{" "}
              <strong className="text-foreground">60 seconds</strong>.
              AI-powered summaries from all your systems, ready for tumor board
              decisions.
            </p>

            <form
              data-animate-hero-cta
              onSubmit={handleWatch}
              className="mx-auto mt-8 flex max-w-md flex-col gap-3 sm:flex-row lg:mx-0 lg:max-w-none"
            >
              <div className="flex-grow">
                <label htmlFor="hero-email" className="sr-only">
                  Enter your work email
                </label>
                <Input
                  id="hero-email"
                  type="email"
                  value={email}
                  onChange={(e) => {
                    setEmail(e.target.value);
                    if (error) setError("");
                  }}
                  placeholder="Enter work email"
                  className={`h-11 w-full lg:w-80 ${
                    error ? "border-destructive focus:ring-destructive" : ""
                  }`}
                  aria-invalid={!!error}
                  aria-describedby="email-error"
                />
                {error && (
                  <p
                    id="email-error"
                    className="mt-2 text-left text-sm text-destructive"
                  >
                    {error}
                  </p>
                )}
              </div>
              <Button
                type="submit"
                size="lg"
                disabled={isSubmitting}
                className="h-11 shrink-0"
              >
                {isSubmitting ? "Submitting..." : "Watch the demo"}
              </Button>
            </form>
          </div>
        </div>
      </div>

      <div className="z-100 absolute inset-0 flex items-center justify-start">
        <OncologyFlow />
      </div>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="aspect-video h-auto w-[85vw] max-w-4xl overflow-hidden rounded-xl border-0 p-0 shadow-2xl">
          {open && (
            <iframe
              key="demo-video"
              className="h-full w-full"
              src="https://www.youtube.com/embed/CDz3KzEaDSU?autoplay=1&rel=0&modestbranding=1"
              title="Entheory Demo"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              allowFullScreen
            />
          )}
        </DialogContent>
      </Dialog>
    </section>
  );
};

export default Hero;
