import type { Config } from "tailwindcss";
import tailwindcssAnimate from "tailwindcss-animate";

export default {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      fontFamily: {
        sans: [
          "Inter",
          "system-ui",
          "-apple-system",
          "BlinkMacSystemFont",
          "Segoe UI",
          "sans-serif",
        ],
        display: ["Inter", "system-ui", "sans-serif"],
      },
      fontSize: {
        caption: [
          "0.625rem",
          { lineHeight: "0.875rem", letterSpacing: "0.025em" },
        ],
        "body-xs": ["0.75rem", { lineHeight: "1rem", letterSpacing: "0em" }],
        "body-sm": [
          "0.875rem",
          { lineHeight: "1.25rem", letterSpacing: "0em" },
        ],
        "body-md": ["1rem", { lineHeight: "1.5rem", letterSpacing: "0em" }],
        "body-lg": [
          "1.125rem",
          { lineHeight: "1.625rem", letterSpacing: "0em" },
        ],
        "heading-6": [
          "1rem",
          { lineHeight: "1.5rem", letterSpacing: "-0.01em" },
        ],
        "heading-5": [
          "1.25rem",
          { lineHeight: "1.75rem", letterSpacing: "-0.01em" },
        ],
        "heading-4": [
          "1.5rem",
          { lineHeight: "2rem", letterSpacing: "-0.02em" },
        ],
        "heading-3": [
          "1.875rem",
          { lineHeight: "2.25rem", letterSpacing: "-0.02em" },
        ],
        "heading-2": [
          "2.25rem",
          { lineHeight: "2.75rem", letterSpacing: "-0.03em" },
        ],
        "heading-1": [
          "2.75rem",
          { lineHeight: "3.25rem", letterSpacing: "-0.03em" },
        ],
        "display-sm": [
          "3rem",
          { lineHeight: "3.5rem", letterSpacing: "-0.04em" },
        ],
        "display-md": [
          "3.75rem",
          { lineHeight: "4.25rem", letterSpacing: "-0.04em" },
        ],
        "display-lg": [
          "4.5rem",
          { lineHeight: "5rem", letterSpacing: "-0.05em" },
        ],
        xs: "0.75rem",
        sm: "0.875rem",
        base: "1rem",
        lg: "1.125rem",
        xl: "1.25rem",
        "2xl": "1.5rem",
        "3xl": "1.875rem",
        "4xl": "2.25rem",
        "5xl": "2.5rem",
        "6xl": "3rem",
        "7xl": "3.75rem",
        "8xl": "4.5rem",
        "9xl": "6rem",
      },
      backgroundImage: {
        "gradient-purple": "var(--gradient-purple)",
        "gradient-orange": "var(--gradient-orange)",
        "gradient-blue": "var(--gradient-blue)",
        "gradient-green": "var(--gradient-green)",
        "gradient-hero": "var(--gradient-hero)",
        "gradient-soft": "var(--gradient-soft)",
        "gradient-medical": "var(--gradient-medical)",
        "gradient-trust": "var(--gradient-trust)",
        "gradient-hero-medical": "var(--gradient-hero-medical)",
      },
      boxShadow: {
        soft: "var(--shadow-soft)",
        medium: "var(--shadow-medium)",
        organic: "var(--shadow-organic)",
      },
      transitionTimingFunction: {
        organic: "var(--transition-organic)",
        bounce: "var(--transition-bounce)",
      },
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        brand: {
          primary: "hsl(var(--brand-primary))",
          "primary-light": "hsl(var(--brand-primary-light))",
          "primary-lighter": "hsl(var(--brand-primary-lighter))",
          "primary-dark": "hsl(var(--brand-primary-dark))",
          "primary-darker": "hsl(var(--brand-primary-darker))",
          "primary-foreground": "hsl(var(--brand-primary-foreground))",
          secondary: "hsl(var(--brand-secondary))",
          "secondary-light": "hsl(var(--brand-secondary-light))",
          "secondary-dark": "hsl(var(--brand-secondary-dark))",
          "secondary-foreground": "hsl(var(--brand-secondary-foreground))",
        },
        medical: {
          critical: "hsl(var(--medical-critical))",
          "critical-light": "hsl(var(--medical-critical-light))",
          "critical-foreground": "hsl(var(--medical-critical-foreground))",
          warning: "hsl(var(--medical-warning))",
          "warning-light": "hsl(var(--medical-warning-light))",
          "warning-foreground": "hsl(var(--medical-warning-foreground))",
          success: "hsl(var(--medical-success))",
          "success-light": "hsl(var(--medical-success-light))",
          "success-foreground": "hsl(var(--medical-success-foreground))",
          info: "hsl(var(--medical-info))",
          "info-light": "hsl(var(--medical-info-light))",
          "info-foreground": "hsl(var(--medical-info-foreground))",
          neutral: "hsl(var(--medical-neutral))",
          "neutral-light": "hsl(var(--medical-neutral-light))",
          "neutral-foreground": "hsl(var(--medical-neutral-foreground))",
        },
        trust: {
          verified: "hsl(var(--trust-verified))",
          secure: "hsl(var(--trust-secure))",
          compliant: "hsl(var(--trust-compliant))",
        },
        cta: {
          primary: "hsl(var(--cta-primary))",
          "primary-hover": "hsl(var(--cta-primary-hover))",
          secondary: "hsl(var(--cta-secondary))",
          success: "hsl(var(--cta-success))",
          danger: "hsl(var(--cta-danger))",
        },
        interactive: {
          hover: "hsl(var(--interactive-hover))",
          active: "hsl(var(--interactive-active))",
          focus: "hsl(var(--interactive-focus))",
          disabled: "hsl(var(--interactive-disabled))",
        },
        neutral: {
          50: "hsl(var(--neutral-50))",
          100: "hsl(var(--neutral-100))",
          200: "hsl(var(--neutral-200))",
          300: "hsl(var(--neutral-300))",
          400: "hsl(var(--neutral-400))",
          500: "hsl(var(--neutral-500))",
          600: "hsl(var(--neutral-600))",
          700: "hsl(var(--neutral-700))",
          800: "hsl(var(--neutral-800))",
          900: "hsl(var(--neutral-900))",
        },
        surface: {
          warm: "hsl(var(--surface-warm))",
          cool: "hsl(var(--surface-cool))",
          soft: "hsl(var(--surface-soft))",
          elevated: "hsl(var(--surface-elevated))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
          light: "hsl(var(--primary-light))",
          lighter: "hsl(var(--primary-lighter))",
          dark: "hsl(var(--primary-dark))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
          accent: "hsl(var(--secondary-accent))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
          light: "hsl(var(--accent-light))",
          secondary: "hsl(var(--accent-secondary))",
        },
        success: {
          DEFAULT: "hsl(var(--success))",
          foreground: "hsl(var(--success-foreground))",
          light: "hsl(var(--success-light))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        fadeIn: {
          from: { opacity: "0", transform: "translateY(10px)" },
          to: { opacity: "1", transform: "translateY(0)" },
        },
        shimmer: {
          "0%": { backgroundPosition: "200% 0" },
          "100%": { backgroundPosition: "-200% 0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        float: "float 20s ease-in-out infinite",
        fadeIn: "fadeIn 0.8s ease-in-out",
        spin: "spin 1s linear infinite",
        pulse: "pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",
        shimmer: "shimmer 2s linear infinite",
      },
    },
  },
  plugins: [tailwindcssAnimate],
} satisfies Config;
