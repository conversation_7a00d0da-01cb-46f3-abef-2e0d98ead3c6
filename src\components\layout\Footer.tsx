import { Linkedin, Mail, MapPin, Phone, Twitter } from "lucide-react";
import React from "react";

const Footer: React.FC = () => {
  return (
    <footer className="bg-slate-900 text-slate-300">
      <div className="container-optimized py-12">
        <div className="grid gap-8 lg:grid-cols-12">
          {/* Company Info */}
          <div className="lg:col-span-4">
            <div className="flex items-center space-x-3">
              <div className="rounded-lg bg-white p-1">
                <img src="/logo.png" alt="logo" className="h-16 w-auto" />
              </div>
            </div>
            <p className="mt-4 text-sm leading-relaxed text-slate-400">
              Revolutionizing cancer care through AI-powered healthcare
              interoperability, enabling evidence-based decisions that save
              lives across India.
            </p>
            <div className="mt-6 flex space-x-4">
              <a
                href="https://linkedin.com/company/entheory-ai"
                target="_blank"
                rel="noopener noreferrer"
                className="text-slate-400 transition-colors hover:text-white"
                aria-label="LinkedIn"
              >
                <Linkedin className="h-5 w-5" />
              </a>
              <a
                href="https://twitter.com/entheory-ai"
                target="_blank"
                rel="noopener noreferrer"
                className="text-slate-400 transition-colors hover:text-white"
                aria-label="Twitter"
              >
                <Twitter className="h-5 w-5" />
              </a>
              <a
                href="mailto:<EMAIL>"
                className="text-slate-400 transition-colors hover:text-white"
                aria-label="Email"
              >
                <Mail className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Navigation & Contact Links */}
          <div className="grid grid-cols-2 gap-8 md:grid-cols-4 lg:col-span-8">
            <div>
              <h4 className="font-semibold text-white">Solutions</h4>
              <ul className="mt-4 space-y-3 text-sm">
                <li>
                  <a
                    href="#"
                    className="text-slate-400 transition-colors hover:text-white"
                  >
                    Data Aggregation
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-slate-400 transition-colors hover:text-white"
                  >
                    Longitudinal Profiles
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-slate-400 transition-colors hover:text-white"
                  >
                    AI-Generated Reports
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-slate-400 transition-colors hover:text-white"
                  >
                    FHIR Compliance
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-slate-400 transition-colors hover:text-white"
                  >
                    ABDM Integration
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-white">Resources</h4>
              <ul className="mt-4 space-y-3 text-sm">
                <li>
                  <a
                    href="#"
                    className="text-slate-400 transition-colors hover:text-white"
                  >
                    White Paper
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-slate-400 transition-colors hover:text-white"
                  >
                    Documentation
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-slate-400 transition-colors hover:text-white"
                  >
                    API Reference
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-slate-400 transition-colors hover:text-white"
                  >
                    Case Studies
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-slate-400 transition-colors hover:text-white"
                  >
                    Support
                  </a>
                </li>
              </ul>
            </div>
            <div className="col-span-2 md:col-span-2">
              <h4 className="font-semibold text-white">Contact</h4>
              <div className="mt-4 space-y-3 text-sm">
                <div className="flex items-start gap-3">
                  <MapPin className="mt-0.5 h-5 w-5 flex-shrink-0 text-slate-500" />
                  <span className="text-slate-400">
                    Bangalore, Karnataka
                    <br />
                    India
                  </span>
                </div>
                <div className="flex items-center gap-3">
                  <Mail className="h-5 w-5 flex-shrink-0 text-slate-500" />
                  <a
                    href="mailto:<EMAIL>"
                    className="text-slate-400 transition-colors hover:text-white"
                  >
                    <EMAIL>
                  </a>
                </div>
                <div className="flex items-center gap-3">
                  <Phone className="h-5 w-5 flex-shrink-0 text-slate-500" />
                  <span className="text-slate-400">+91 XXX XXX XXXX</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-12 border-t border-slate-800 pt-8">
          <div className="flex flex-col items-center justify-between gap-4 md:flex-row">
            <p className="text-sm text-slate-500">
              © {new Date().getFullYear()} Entheory. All rights reserved.
            </p>
            <div className="flex space-x-6 text-sm text-slate-500">
              <a href="#" className="transition-colors hover:text-white">
                Privacy Policy
              </a>
              <a href="#" className="transition-colors hover:text-white">
                Terms of Service
              </a>
              <a href="#" className="transition-colors hover:text-white">
                Cookie Policy
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
